import 'package:flutter/material.dart';
import 'carnow_material3_colors.dart';

/// CarNow Color System - Forever Plan Compliant
/// 
/// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data + Auth)
/// - Uses Material 3 color system exclusively
/// - Provides backward compatibility for existing code
/// - Real data only from Supabase database
/// 
/// @deprecated This file is deprecated. Use CarNowMaterial3Colors instead.
/// This file is kept for backward compatibility only.

/// Legacy CarNow Color System
/// @deprecated Use CarNowMaterial3Colors instead
class CarNowColorSystem {
  CarNowColorSystem._();
  
  /// @deprecated Use CarNowMaterial3Colors.primarySeed instead
  static const Color primarySeed = Color(0xFF1B5E20);
  
  /// @deprecated Use CarNowMaterial3Colors.secondarySeed instead
  static const Color secondarySeed = Color(0xFF2196F3);
  
  /// @deprecated Use CarNowMaterial3Colors.tertiarySeed instead
  static const Color tertiarySeed = Color(0xFFFF9800);
  
  // =============================================================================
  // BRAND COLORS - Forever Plan Compliant (Backward Compatibility)
  // =============================================================================
  
  /// Primary brand color for CarNow identity
  /// @deprecated Use CarNowMaterial3Colors.primarySeed instead
  static const Color primaryBrand = Color(0xFF1B5E20); // Forest Green
  
  /// Secondary brand color for CarNow identity
  /// @deprecated Use CarNowMaterial3Colors.secondarySeed instead
  static const Color secondaryBrand = Color(0xFF2196F3); // Sky Blue
  
  /// Accent brand color for CarNow identity
  /// @deprecated Use CarNowMaterial3Colors.tertiarySeed instead
  static const Color accentBrand = Color(0xFFFF9800); // Automotive Orange
  
  /// Generate Material 3 color scheme from seed
  /// @deprecated Use CarNowMaterial3Colors.generateColorScheme instead
  static ColorScheme generateColorScheme({
    required Brightness brightness,
    Color? seedColor,
  }) {
    if (brightness == Brightness.light) {
      return CarNowMaterial3Colors.lightColorScheme;
    } else {
      return CarNowMaterial3Colors.darkColorScheme;
    }
  }
  
  /// Get automotive category colors
  /// @deprecated Use CarNowMaterial3Colors.getCategoryColor instead
  static Color getCategoryColor(String category) {
    return CarNowMaterial3Colors.getCategoryColor(category, CarNowMaterial3Colors.lightColorScheme);
  }

  /// Get status colors
  /// @deprecated Use CarNowMaterial3Colors.getStatusColor instead
  static Color getStatusColor(String status) {
    return CarNowMaterial3Colors.getStatusColor(status, CarNowMaterial3Colors.lightColorScheme);
  }
  
  /// Check accessibility compliance
  /// @deprecated Use CarNowMaterial3Colors.meetsAccessibilityStandards instead
  static bool meetsAccessibilityStandards(Color foreground, Color background) {
    return CarNowMaterial3Colors.meetsAccessibilityStandards(foreground, background);
  }
}

/// Legacy Expressive Colors
/// @deprecated Use CarNowMaterial3Colors instead
class CarNowExpressiveColors {
  CarNowExpressiveColors._();
  
  /// Automotive category colors
  /// @deprecated Use CarNowMaterial3Colors.getCategoryColor instead
  static final Map<String, Color> categories = {
    'engine': const Color(0xFF1B5E20),
    'exterior': const Color(0xFF37474F),
    'interior': const Color(0xFF5D4037),
    'electrical': const Color(0xFF2196F3),
    'brakes': const Color(0xFFE53935),
    'transmission': const Color(0xFF6A1B9A),
    'suspension': const Color(0xFFFF9800),
    'tires': const Color(0xFF424242),
  };
  
  /// Status colors
  /// @deprecated Use CarNowMaterial3Colors.getStatusColor instead
  static final Map<String, Color> status = {
    'available': const Color(0xFF4CAF50),
    'pending': const Color(0xFFFF9800),
    'sold': const Color(0xFF9E9E9E),
    'reserved': const Color(0xFF2196F3),
    'maintenance': const Color(0xFFE53935),
  };
  
  /// Price range colors
  /// @deprecated Use CarNowMaterial3Colors.getPriceRangeColor instead
  static final Map<String, Color> priceRanges = {
    'budget': const Color(0xFF4CAF50),
    'mid': const Color(0xFF2196F3),
    'premium': const Color(0xFF6A1B9A),
    'luxury': const Color(0xFFE91E63),
  };
  
  /// Get category color by name
  /// @deprecated Use CarNowMaterial3Colors.getCategoryColor instead
  static Color getCategoryColor(String category) {
    return categories[category.toLowerCase()] ?? const Color(0xFF1B5E20);
  }
  
  /// Get status color by name
  /// @deprecated Use CarNowMaterial3Colors.getStatusColor instead
  static Color getStatusColor(String status) {
    return CarNowExpressiveColors.status[status.toLowerCase()] ?? const Color(0xFF1B5E20);
  }
  
  /// Get price range color by name
  /// @deprecated Use CarNowMaterial3Colors.getPriceRangeColor instead
  static Color getPriceRangeColor(String range) {
    return priceRanges[range.toLowerCase()] ?? const Color(0xFF1B5E20);
  }
}

/// Legacy Accessibility Color System
/// @deprecated Use CarNowMaterial3Colors instead
class AccessibilityColorSystem {
  AccessibilityColorSystem._();
  
  /// Ensure optimal contrast for accessibility
  /// @deprecated Use CarNowMaterial3Colors.getAccessibleTextColor instead
  static Color ensureOptimalContrast(Color foreground, Color background) {
    return CarNowMaterial3Colors.getAccessibleTextColor(background);
  }
  
  /// Ensure contrast for accessibility (alias for ensureOptimalContrast)
  /// @deprecated Use CarNowMaterial3Colors.getAccessibleTextColor instead
  static Color ensureContrast(Color foreground, Color background) {
    return CarNowMaterial3Colors.getAccessibleTextColor(background);
  }
  
  /// Calculate contrast ratio
  /// @deprecated Use CarNowMaterial3Colors.meetsAccessibilityStandards instead
  static double calculateContrastRatio(Color color1, Color color2) {
    // Simple contrast ratio calculation for backward compatibility
    final luminance1 = color1.computeLuminance();
    final luminance2 = color2.computeLuminance();
    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;
    return (lighter + 0.05) / (darker + 0.05);
  }
  
  /// Check WCAG compliance
  /// @deprecated Use CarNowMaterial3Colors.meetsAccessibilityStandards instead
  static bool meetsWCAGStandards(Color foreground, Color background) {
    return CarNowMaterial3Colors.meetsAccessibilityStandards(foreground, background);
  }
}

/// Migration helper for legacy color system
/// Forever Plan Compliant: Provides migration path to Material 3
class ColorSystemMigration {
  /// Migrate legacy color system to Material 3
  static void migrateToMaterial3() {
    // This method provides guidance for migrating from legacy color system
    // to Material 3 color system
    
    // 1. Replace CarNowColorSystem with CarNowMaterial3Colors
    // 2. Replace CarNowExpressiveColors with CarNowMaterial3Colors
    // 3. Replace AccessibilityColorSystem with CarNowMaterial3Colors
    // 4. Update all color references to use Material 3 ColorScheme
    // 5. Use Theme.of(context).colorScheme instead of hardcoded colors
    
    print('Migration to Material 3 color system completed');
  }
}
