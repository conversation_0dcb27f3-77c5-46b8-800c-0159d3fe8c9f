import 'dart:async';

/// Result of OAuth deep link handling
class OAuthDeepLinkResult {
  final bool isSuccess;
  final String? authCode;
  final String? accessToken;
  final String? state;
  final String? error;

  const OAuthDeepLinkResult({
    required this.isSuccess,
    this.authCode,
    this.accessToken,
    this.state,
    this.error,
  });

  factory OAuthDeepLinkResult.success({
    String? authCode,
    String? accessToken,
    String? state,
  }) {
    return OAuthDeepLinkResult(
      isSuccess: true,
      authCode: authCode,
      accessToken: accessToken,
      state: state,
    );
  }

  factory OAuthDeepLinkResult.error(String error) {
    return OAuthDeepLinkResult(
      isSuccess: false,
      error: error,
    );
  }
}

/// Handles OAuth deep links for authentication
class DeepLinkHandler {
  static const String _expectedScheme = 'io.supabase.carnow';
  static const List<String> _validHosts = ['login-callback', 'auth-callback'];

  /// Handles OAuth callback deep links
  Future<OAuthDeepLinkResult> handleOAuthCallback(String deepLink) async {
    try {
      if (deepLink.isEmpty) {
        return OAuthDeepLinkResult.error('Invalid deep link scheme');
      }

      final uri = Uri.parse(deepLink);

      // Check scheme
      if (uri.scheme != _expectedScheme) {
        return OAuthDeepLinkResult.error('Invalid deep link scheme');
      }

      // Check host
      if (!_validHosts.contains(uri.host)) {
        return OAuthDeepLinkResult.error('Invalid deep link scheme');
      }

      // Check for error parameters first
      final error = extractError(deepLink);
      if (error != null) {
        return OAuthDeepLinkResult.error(error);
      }

      // Extract auth parameters
      final authCode = extractAuthCode(deepLink);
      final accessToken = extractAccessToken(deepLink);
      final state = extractState(deepLink);

      // Must have either auth code or access token
      if (authCode == null && accessToken == null) {
        return OAuthDeepLinkResult.error('Missing required parameters');
      }

      return OAuthDeepLinkResult.success(
        authCode: authCode,
        accessToken: accessToken,
        state: state,
      );
    } catch (e) {
      return OAuthDeepLinkResult.error('Failed to parse deep link: $e');
    }
  }

  /// Validates if the deep link is a valid OAuth callback
  bool isValidOAuthDeepLink(String deepLink) {
    if (deepLink.isEmpty) return false;

    try {
      final uri = Uri.parse(deepLink);

      // Check scheme
      if (uri.scheme != _expectedScheme) return false;

      // Check host
      if (!_validHosts.contains(uri.host)) return false;

      // Must have either code, access_token, or error
      final hasCode = uri.queryParameters.containsKey('code');
      final hasAccessToken = uri.queryParameters.containsKey('access_token');
      final hasError = uri.queryParameters.containsKey('error');

      return hasCode || hasAccessToken || hasError;
    } catch (e) {
      return false;
    }
  }

  /// Extracts authorization code from deep link
  String? extractAuthCode(String deepLink) {
    try {
      final uri = Uri.parse(deepLink);
      return uri.queryParameters['code'];
    } catch (e) {
      return null;
    }
  }

  /// Extracts access token from deep link
  String? extractAccessToken(String deepLink) {
    try {
      final uri = Uri.parse(deepLink);
      return uri.queryParameters['access_token'];
    } catch (e) {
      return null;
    }
  }

  /// Extracts state parameter from deep link
  String? extractState(String deepLink) {
    try {
      final uri = Uri.parse(deepLink);
      return uri.queryParameters['state'];
    } catch (e) {
      return null;
    }
  }

  /// Extracts error information from deep link
  String? extractError(String deepLink) {
    try {
      final uri = Uri.parse(deepLink);
      final error = uri.queryParameters['error'];
      final errorDescription = uri.queryParameters['error_description'];
      
      if (error == null) return null;
      
      if (errorDescription != null) {
        return '$error: ${Uri.decodeComponent(errorDescription)}';
      }
      
      return error;
    } catch (e) {
      return null;
    }
  }
}
