// ============================================================================
// AUTH ERROR HANDLER – Forever Plan Architecture
// ============================================================================
/// Handles mapping of authentication-related error codes to user-friendly
/// messages and determines whether the error can be automatically recovered
/// from (e.g. retry).
///
/// This utility is intentionally **pure** (no external dependencies) so it can
/// be reused easily across layers (backend mocks, unit tests, etc.).
///
/// The default language for [getLocalizedMessage] is Arabic because the tests
/// expect an Arabic translation when no locale is supplied.
/// ============================================================================

library;

import 'package:flutter/foundation.dart';

/// Centralised authentication error handler.
@immutable
class AuthErrorHandler {
  // ---------------------------------------------------------------------------
  // Supported error codes
  // ---------------------------------------------------------------------------
  static const String invalidCredentials = 'INVALID_CREDENTIALS';
  static const String networkError = 'NETWORK_ERROR';
  static const String unknownError = 'UNKNOWN_ERROR';

  // ---------------------------------------------------------------------------
  // English messages
  // ---------------------------------------------------------------------------
  static const _messagesEn = <String, String>{
    invalidCredentials: 'Invalid email or password',
    networkError: 'Network error. Please try again',
    unknownError: 'An unknown error occurred',
  };

  // ---------------------------------------------------------------------------
  // Arabic messages (default localisation)
  // ---------------------------------------------------------------------------
  static const _messagesAr = <String, String>{
    invalidCredentials: 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
    networkError: 'خطأ في الشبكة. حاول مرة أخرى',
    unknownError: 'حدث خطأ غير معروف',
  };

  // ---------------------------------------------------------------------------
  // Public API
  // ---------------------------------------------------------------------------

  /// Returns the **English** message for the provided [errorCode].
  /// Falls back to a generic message if the code is unrecognised.
  String handleError(String errorCode) =>
      _messagesEn[errorCode] ?? _messagesEn[unknownError]!;

  /// Returns the **localised** message for the provided [errorCode].
  ///
  /// If [locale] is omitted, Arabic (\'ar\') is used because the project
  /// currently targets Arabic as the primary localisation.
  String getLocalizedMessage(
    String errorCode, {
    String locale = 'ar',
  }) {
    if (locale.startsWith('ar')) {
      return _messagesAr[errorCode] ?? _messagesAr[unknownError]!;
    }
    return _messagesEn[errorCode] ?? _messagesEn[unknownError]!;
  }

  /// Indicates whether an error is recoverable by retrying the same action
  /// without user intervention (e.g. transient network issues).
  bool isRecoverableError(String errorCode) =>
      errorCode == networkError; // Extend as needed.
}