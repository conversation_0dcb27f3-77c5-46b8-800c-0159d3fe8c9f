// ============================================================================
// EDIT VEHICLE SCREEN - Forever Plan Architecture
// ============================================================================
// 
// Screen for editing vehicle information.
// Following Forever Plan principles:
// - REAL DATA ONLY from providers
// - NO MOCK DATA tolerance
// - Material 3 Design System
// - Accessibility compliant
// - Performance optimized
//
// ============================================================================

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:carnow/core/widgets/app_error_widget.dart';
import 'package:carnow/features/garage/providers/garage_providers.dart';
import 'package:carnow/features/garage/widgets/vehicle_details_form.dart';
import 'package:carnow/features/garage/models/garage_models.dart';

class EditVehicleScreen extends ConsumerStatefulWidget {
  final String vehicleId;

  const EditVehicleScreen({
    super.key,
    required this.vehicleId,
  });

  @override
  ConsumerState<EditVehicleScreen> createState() => _EditVehicleScreenState();
}

class _EditVehicleScreenState extends ConsumerState<EditVehicleScreen> {
  final _formKey = GlobalKey<FormState>();
  final _colorController = TextEditingController();
  final _vinController = TextEditingController();
  final _licensePlateController = TextEditingController();
  final _mileageController = TextEditingController();
  final _notesController = TextEditingController();
  bool _isPrimary = false;
  UserVehicle? _vehicle;

  @override
  void dispose() {
    _colorController.dispose();
    _vinController.dispose();
    _licensePlateController.dispose();
    _mileageController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _initializeForm(UserVehicle vehicle) {
    if (_vehicle?.id == vehicle.id) return; // Already initialized

    _vehicle = vehicle;
    _colorController.text = vehicle.color ?? '';
    _vinController.text = vehicle.vin ?? '';
    _licensePlateController.text = vehicle.licensePlate ?? '';
    _mileageController.text = vehicle.mileage > 0 ? vehicle.mileage.toString() : '';
    _notesController.text = ''; // Notes not available in current model
    _isPrimary = vehicle.isPrimary;
  }

  @override
  Widget build(BuildContext context) {
    final vehiclesAsync = ref.watch(myVehiclesProvider);
    final garageActions = ref.watch(garageActionsProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('تعديل السيارة'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        scrolledUnderElevation: 1,
        actions: [
          TextButton(
            onPressed: garageActions.isLoading ? null : _saveChanges,
            child: garageActions.isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('حفظ'),
          ),
        ],
      ),
      body: vehiclesAsync.when(
        data: (vehicles) {
          final vehicle = vehicles.firstWhere(
            (v) => v.id == widget.vehicleId,
            orElse: () => throw StateError('Vehicle not found'),
          );

          // Initialize form with vehicle data
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _initializeForm(vehicle);
          });

          return Column(
            children: [
              // Vehicle info header
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  border: Border(
                    bottom: BorderSide(
                      color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.directions_car_rounded,
                        color: Theme.of(context).colorScheme.primary,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${vehicle.year} ${vehicle.make} ${vehicle.model}',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          if (vehicle.trim?.isNotEmpty == true) ...[
                            const SizedBox(height: 2),
                            Text(
                              vehicle.trim!,
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Form content
              Expanded(
                child: VehicleDetailsForm(
                  formKey: _formKey,
                  colorController: _colorController,
                  vinController: _vinController,
                  licensePlateController: _licensePlateController,
                  mileageController: _mileageController,
                  notesController: _notesController,
                  isPrimary: _isPrimary,
                  onIsPrimaryChanged: (value) => setState(() => _isPrimary = value),
                ),
              ),

              // Bottom actions
              Container(
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  border: Border(
                    top: BorderSide(
                      color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: garageActions.isLoading ? null : () => context.pop(),
                        child: const Text('إلغاء'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: FilledButton(
                        onPressed: garageActions.isLoading ? null : _saveChanges,
                        style: FilledButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.primary,
                        ),
                        child: garageActions.isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : const Text('حفظ التغييرات'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
        loading: () => const Scaffold(
          body: Center(child: CircularProgressIndicator()),
        ),
        error: (error, stackTrace) => Scaffold(
          appBar: AppBar(title: const Text('خطأ')),
          body: Center(
            child: AppErrorWidget(
              message: 'خطأ في تحميل بيانات السيارة',
              details: error.toString(),
              onRetry: () => ref.invalidate(myVehiclesProvider),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) return;

    if (_vehicle == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('خطأ في تحميل بيانات السيارة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      await ref.read(garageActionsProvider.notifier).updateVehicle(
        vehicleId: int.parse(widget.vehicleId),
        color: _colorController.text.isNotEmpty ? _colorController.text : null,
        vin: _vinController.text.isNotEmpty ? _vinController.text : null,
        licensePlate: _licensePlateController.text.isNotEmpty ? _licensePlateController.text : null,
        mileage: _mileageController.text.isNotEmpty ? int.tryParse(_mileageController.text) : null,
        isPrimary: _isPrimary,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('تم حفظ التغييرات بنجاح'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );

        // Navigate back
        context.pop();
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ التغييرات: $error'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}
