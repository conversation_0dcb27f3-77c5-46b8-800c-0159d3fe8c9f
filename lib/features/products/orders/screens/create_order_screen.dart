/// شاشة إنشاء الطلب
///
/// تبدأ عملية الدفع والشراء من هذه الشاشة.
/// تعرض ملخصًا للمنتج المطلوب، وتسمح للمستخدم بتأكيد عنوان الشحن
/// واختيار طريقة الدفع لإتمام عملية الشراء.
library;

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/models/enums.dart';
import '../../models/product_model.dart';
import 'package:carnow/shared/widgets/primary_button.dart';
import '../../../../core/auth/simple_supabase_auth_provider.dart';

import '../../../../core/repositories/order_repository.dart';
import '../../../../core/utils/formatters.dart';
import '../../../../core/models/order_item_model.dart';
import '../../../../core/models/order_model.dart';

class CreateOrderScreen extends HookConsumerWidget {
  const CreateOrderScreen({required this.listing, super.key});
  final ProductModel listing;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAuthenticated = ref.watch(isAuthenticatedProvider);
    final user = ref.watch(currentUserProvider);
    final orderRepository = ref.read(orderRepositoryProvider);

    final quantityController = useTextEditingController(text: '1');
    final shippingAddressController = useTextEditingController();
    final formKey = useMemoized(GlobalKey<FormState>.new);
    final isLoading = useState(false);

    Future<void> submitOrder() async {
      final messenger = ScaffoldMessenger.of(context);
      final navigator = Navigator.of(context);
      if (!formKey.currentState!.validate()) {
        return;
      }

      if (!isAuthenticated || user == null) {
        messenger.showSnackBar(
          const SnackBar(content: Text('Please log in to place an order.')),
        );
        return;
      }

      final quantity = int.tryParse(quantityController.text) ?? 1;
      final shippingAddress = shippingAddressController.text;

      final orderItem = OrderItemModel(
        productId: listing.id,
        quantity: quantity,
        unitPrice: listing.price,
      );

      final newOrder = OrderModel(
        id: 0,
        buyerId: int.tryParse(user.id),
        totalAmount: listing.price * quantity,
        status: OrderStatus.pending,
        shippingAddress: shippingAddress.isNotEmpty ? shippingAddress : null,
        createdAt: DateTime.now(),
        items: [orderItem],
      );

      isLoading.value = true;
      try {
        await orderRepository.createOrder(newOrder);
        messenger.showSnackBar(
          const SnackBar(content: Text('Order placed successfully!')),
        );
        navigator.pop();
      } catch (e) {
        messenger.showSnackBar(
          SnackBar(content: Text('Failed to place order: $e')),
        );
      } finally {
        isLoading.value = false;
      }
    }

    return Scaffold(
      appBar: AppBar(title: const Text('Place Order')),
      body: Form(
        key: formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildListingSummary(context, listing),
            const SizedBox(height: 24),
            TextFormField(
              controller: quantityController,
              decoration: const InputDecoration(labelText: 'Quantity'),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter quantity';
                }
                final quantity = int.tryParse(value);
                if (quantity == null || quantity <= 0) {
                  return 'Please enter a valid quantity';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: shippingAddressController,
              decoration: const InputDecoration(
                labelText: 'Shipping Address (Optional)',
              ),
              maxLines: 3,
              textInputAction: TextInputAction.done,
            ),
            const SizedBox(height: 32),
            PrimaryButton(
              text: 'Confirm Order',
              onPressed: isLoading.value ? null : submitOrder,
              isLoading: isLoading.value,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildListingSummary(BuildContext context, ProductModel listing) {
    final textTheme = Theme.of(context).textTheme;
    const paddingXS = 4.0;

    return Card(
      elevation: 0,
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(listing.name, style: textTheme.titleMedium),
                  const SizedBox(height: paddingXS),
                  Text(
                    Formatters.formatCurrency(listing.price),
                    style: textTheme.titleSmall!.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: paddingXS),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
