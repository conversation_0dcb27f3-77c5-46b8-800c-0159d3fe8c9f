import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../models/wallet_model.dart';
import '../providers/financial_operations_providers.dart';
import 'package:carnow/features/wallet/widgets/wallet_verification_card.dart';
import 'package:carnow/features/wallet/widgets/wallet_limits_card.dart';
import 'package:carnow/features/wallet/widgets/wallet_transaction_item.dart';
import 'package:carnow/features/wallet/widgets/wallet_action_buttons.dart';
import 'package:carnow/features/wallet/widgets/empty_transactions_widget.dart';

/// شاشة المحفظة الرئيسية
class WalletScreen extends ConsumerStatefulWidget {
  const WalletScreen({super.key});

  @override
  ConsumerState<WalletScreen> createState() => _WalletScreenState();
}

class _WalletScreenState extends ConsumerState<WalletScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('المحفظة'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () => context.push('/wallet/history'),
            tooltip: 'سجل المعاملات',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => context.push('/wallet/settings'),
            tooltip: 'إعدادات المحفظة',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الرئيسية', icon: Icon(Icons.home)),
            Tab(text: 'المعاملات', icon: Icon(Icons.receipt_long)),
            Tab(text: 'الإعدادات', icon: Icon(Icons.settings)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildMainTab(),
          _buildTransactionsTab(),
          _buildSettingsTab(),
        ],
      ),
    );
  }

  /// تبويب الصفحة الرئيسية
  Widget _buildMainTab() {
    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(walletDataProvider);
        ref.invalidate(walletSummaryProvider);
      },
      child: SingleChildScrollView(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة الرصيد
            _buildWalletBalanceSection(),
            const SizedBox(height: 16),

            // أزرار الإجراءات السريعة
            _buildQuickActionsSection(),
            const SizedBox(height: 16),

            // بطاقة التحقق
            _buildVerificationSection(),
            const SizedBox(height: 16),

            // بطاقة الحدود
            _buildLimitsSection(),
            const SizedBox(height: 16),

            // المعاملات الأخيرة
            _buildRecentTransactionsSection(),
          ],
        ),
      ),
    );
  }

  /// قسم رصيد المحفظة
  Widget _buildWalletBalanceSection() {
    final walletAsync = ref.watch(walletDataProvider);

    return walletAsync.when(
      data: (wallet) => Card(
        margin: const EdgeInsets.only(bottom:16),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('إجمالي الرصيد', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Text('${wallet.balance.toStringAsFixed(2)} ${wallet.currency}'),
            ],
          ),
        ),
      ),
      loading: () => const Card(
        elevation: 2,
        child: SizedBox(
          height: 140,
          child: Center(child: CircularProgressIndicator()),
        ),
      ),
      error: (error, _) => Card(
        color: Theme.of(context).colorScheme.errorContainer,
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Row(
            children: [
              Icon(Icons.error, color: Theme.of(context).colorScheme.error),
              const SizedBox(width: 12),
              Expanded(child: Text(error.toString())),
            ],
          ),
        ),
      ),
    );
  }

  /// قسم الإجراءات السريعة
  Widget _buildQuickActionsSection() {
    return const WalletActionButtons();
  }

  /// قسم حالة التحقق
  Widget _buildVerificationSection() {
    final walletAsync = ref.watch(walletDataProvider);

    return walletAsync.when(
      data: (wallet) => WalletVerificationCard(wallet: wallet),
      loading: () => const WalletVerificationCard.loading(),
      error: (error, _) => WalletVerificationCard.error(error.toString()),
    );
  }

  /// قسم الحدود
  Widget _buildLimitsSection() {
    final walletAsync = ref.watch(walletDataProvider);

    return walletAsync.when(
      data: (wallet) => WalletLimitsCard(wallet: wallet),
      loading: () => const WalletLimitsCard.loading(),
      error: (error, _) => WalletLimitsCard.error(error.toString()),
    );
  }

  /// قسم المعاملات الأخيرة
  Widget _buildRecentTransactionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'المعاملات الأخيرة',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            TextButton(
              onPressed: () => context.push('/wallet/transactions'),
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        _buildRecentTransactionsList(),
      ],
    );
  }

  /// قائمة المعاملات الأخيرة
  Widget _buildRecentTransactionsList() {
    final transactionsAsync = ref.watch(walletTransactionsProvider(limit: 5));

    return transactionsAsync.when(
      data: (transactions) {
        if (transactions.isEmpty) {
          return const EmptyTransactionsWidget();
        }

        return Column(
          children: transactions
              .map(
                (transaction) => WalletTransactionItem(
                  transaction: transaction,
                  onTap: () =>
                      context.push('/wallet/transaction/${transaction.id}'),
                ),
              )
              .toList(),
        );
      },
      loading: () => Column(
        children: List.generate(
          3,
          (index) => WalletTransactionItem.loading(),
        ),
      ),
      error: (error, _) => Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                Icons.error_outline,
                size: 48,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 8),
              Text(
                'خطأ في تحميل المعاملات',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 4),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// تبويب المعاملات
  Widget _buildTransactionsTab() {
    return _TransactionsTabView();
  }

  /// تبويب الإعدادات
  Widget _buildSettingsTab() {
    return _SettingsTabView();
  }
}

/// عرض تبويب المعاملات
class _TransactionsTabView extends ConsumerStatefulWidget {
  @override
  ConsumerState<_TransactionsTabView> createState() =>
      _TransactionsTabViewState();
}

class _TransactionsTabViewState extends ConsumerState<_TransactionsTabView> {
  WalletTransactionType? _selectedType;
  final ScrollController _scrollController = ScrollController();
  int _currentPage = 1;
  List<WalletTransaction> _allTransactions = [];
  bool _isLoadingMore = false;
  bool _hasMore = true;

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // فلتر نوع المعاملة
        _buildTransactionTypeFilter(),

        // قائمة المعاملات
        Expanded(child: _buildTransactionsList()),
      ],
    );
  }

  /// فلتر نوع المعاملة
  Widget _buildTransactionTypeFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            _buildFilterChip('الكل', null),
            const SizedBox(width: 8),
            _buildFilterChip('إيداع', WalletTransactionType.deposit),
            const SizedBox(width: 8),
            _buildFilterChip('سحب', WalletTransactionType.withdrawal),
            const SizedBox(width: 8),
            _buildFilterChip('تحويل وارد', WalletTransactionType.transferIn),
            const SizedBox(width: 8),
            _buildFilterChip('تحويل صادر', WalletTransactionType.transferOut),
            const SizedBox(width: 8),
            _buildFilterChip('دفع', WalletTransactionType.payment),
            const SizedBox(width: 8),
            _buildFilterChip('استرداد', WalletTransactionType.refund),
          ],
        ),
      ),
    );
  }

  /// رقاقة فلتر
  Widget _buildFilterChip(String label, WalletTransactionType? type) {
    final isSelected = _selectedType == type;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedType = selected ? type : null;
        });
      },
    );
  }

  /// قائمة المعاملات
  Widget _buildTransactionsList() {
    final transactionsAsync = ref.watch(
      walletTransactionsProvider(page: _currentPage, type: _selectedType),
    );

    return transactionsAsync.when(
      data: (newTransactions) {
        if (_currentPage == 1) {
          _allTransactions = newTransactions;
        } else {
          _allTransactions.addAll(newTransactions);
        }
        _hasMore = newTransactions.length == 20; // Assuming pageSize=20
        _isLoadingMore = false;

        if (_allTransactions.isEmpty) {
          return const EmptyTransactionsWidget();
        }

        return RefreshIndicator(
          onRefresh: () async {
            _currentPage = 1;
            _allTransactions.clear();
            _hasMore = true;
            ref.invalidate(walletTransactionsProvider);
          },
          child: ListView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.all(16),
            itemCount: _allTransactions.length + (_hasMore ? 1 : 0),
            itemBuilder: (context, index) {
              if (index >= _allTransactions.length) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              final transaction = _allTransactions[index];
              return WalletTransactionItem(
                transaction: transaction,
                onTap: () =>
                    context.push('/wallet/transaction/${transaction.id}'),
              );
            },
          ),
        );
      },
      loading: () => _isLoadingMore
          ? const SizedBox.shrink()
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: 10,
              itemBuilder: (context, index) => WalletTransactionItem.loading(),
            ),
      error: (error, stack) => Center(child: Text('Error: $error')),
    );
  }

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent - 200 &&
          !_isLoadingMore &&
          _hasMore) {
        _isLoadingMore = true;
        _currentPage++;
        ref.read(walletTransactionsProvider(page: _currentPage, type: _selectedType));
      }
    });
  }
}

/// عرض تبويب الإعدادات
class _SettingsTabView extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final walletAsync = ref.watch(walletDataProvider);

    return walletAsync.when(
      data: (wallet) => SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // قسم التحقق
            _buildVerificationSection(context, wallet),
            const SizedBox(height: 24),

            // قسم الحدود
            _buildLimitsSection(context, wallet),
            const SizedBox(height: 24),

            // قسم الأمان
            _buildSecuritySection(context, wallet),
            const SizedBox(height: 24),

            // قسم الإعدادات العامة
            _buildGeneralSettingsSection(context),
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, _) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل إعدادات المحفظة',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// قسم التحقق
  Widget _buildVerificationSection(BuildContext context, WalletModel wallet) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'التحقق من الهوية',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            _buildVerificationItem(
              context,
              'الهاتف المحمول',
              wallet.phoneVerified,
              Icons.phone,
              () => context.push('/wallet/verify/phone'),
            ),
            _buildVerificationItem(
              context,
              'البريد الإلكتروني',
              wallet.emailVerified,
              Icons.email,
              () => context.push('/wallet/verify/email'),
            ),
            _buildVerificationItem(
              context,
              'هوية شخصية',
              wallet.idVerified,
              Icons.badge,
              () => context.push('/wallet/verify/identity'),
            ),
          ],
        ),
      ),
    );
  }

  /// عنصر التحقق
  Widget _buildVerificationItem(
    BuildContext context,
    String title,
    bool isVerified,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(
        icon,
        color: isVerified
            ? Theme.of(context).colorScheme.primary
            : Theme.of(context).colorScheme.outline,
      ),
      title: Text(title),
      trailing: isVerified
          ? Icon(Icons.verified, color: Theme.of(context).colorScheme.primary)
          : TextButton(onPressed: onTap, child: const Text('تحقق')),
      onTap: isVerified ? null : onTap,
    );
  }

  /// قسم الحدود
  Widget _buildLimitsSection(BuildContext context, WalletModel wallet) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الحدود والقيود',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            _buildLimitItem(
              context,
              'الحد اليومي',
              '${wallet.dailyLimit.toStringAsFixed(2)} د.ل',
              '${wallet.dailySpent.toStringAsFixed(2)} د.ل مستخدم',
            ),
            _buildLimitItem(
              context,
              'الحد الشهري',
              '${wallet.monthlyLimit.toStringAsFixed(2)} د.ل',
              '${wallet.monthlySpent.toStringAsFixed(2)} د.ل مستخدم',
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: () => context.push('/wallet/limits'),
              child: const Text('تعديل الحدود'),
            ),
          ],
        ),
      ),
    );
  }

  /// عنصر الحد
  Widget _buildLimitItem(
    BuildContext context,
    String title,
    String limit,
    String used,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title, style: Theme.of(context).textTheme.bodyMedium),
              Text(
                used,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.outline,
                ),
              ),
            ],
          ),
          Text(limit, style: Theme.of(context).textTheme.titleSmall),
        ],
      ),
    );
  }

  /// قسم الأمان
  Widget _buildSecuritySection(BuildContext context, WalletModel wallet) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الأمان', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.lock),
              title: const Text('تغيير كلمة المرور'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => context.push('/wallet/security/password'),
            ),
            ListTile(
              leading: const Icon(Icons.security),
              title: const Text('إعدادات الأمان'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => context.push('/wallet/security/settings'),
            ),
            ListTile(
              leading: const Icon(Icons.history),
              title: const Text('سجل الدخول'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => context.push('/wallet/security/login-history'),
            ),
          ],
        ),
      ),
    );
  }

  /// قسم الإعدادات العامة
  Widget _buildGeneralSettingsSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الإعدادات العامة',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.notifications),
              title: const Text('إشعارات المحفظة'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => context.push('/wallet/notifications'),
            ),
            ListTile(
              leading: const Icon(Icons.help),
              title: const Text('المساعدة والدعم'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => context.push('/wallet/help'),
            ),
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('حول المحفظة'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => context.push('/wallet/about'),
            ),
          ],
        ),
      ),
    );
  }
}
