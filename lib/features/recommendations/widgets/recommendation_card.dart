import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../models/recommendation_model.dart';

/// A theme-aware card for displaying a recommended product.
class RecommendationCard extends StatelessWidget {
  const RecommendationCard({
    required this.recommendation,
    required this.onTap,
    super.key,
    this.showReason = true,
    this.showScore = true,
  });

  final RecommendationModel recommendation;
  final VoidCallback onTap;
  final bool showReason;
  final bool showScore;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    return Card(
      // The card's style is now primarily controlled by the global CardTheme.
      // We can keep specific overrides if necessary, but it's better to rely on the theme.
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image
            _ProductImage(
              imageUrl: recommendation.product.images.first,
              height: 180,
            ),

            // Product Details
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Product Name
                    Text(
                      recommendation.product.name,
                      style: textTheme.titleSmall,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: AppTheme.spacingXXS),

                    // Price
                    Text(
                      '${recommendation.product.price.toStringAsFixed(2)} د.ل',
                      style: textTheme.titleMedium?.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),

                    // Recommendation Info
                    if (showReason) _buildRecommendationInfo(context),

                    // Score
                    if (showScore) _buildScoreIndicator(context),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendationInfo(BuildContext context) {
    final theme = Theme.of(context);
    final typeColor = _getTypeColor(context);
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingS,
        vertical: AppTheme.spacingXXS,
      ),
      decoration: BoxDecoration(
        color: typeColor.withAlpha(25),
        borderRadius: BorderRadius.circular(AppTheme.radiusS),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(_getTypeIcon(), size: 12, color: typeColor),
          const SizedBox(width: AppTheme.spacingXS),
          Flexible(
            child: Text(
              recommendation.reason,
              style: theme.textTheme.labelSmall?.copyWith(
                color: typeColor,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScoreIndicator(BuildContext context) {
    final theme = Theme.of(context);
    final scorePercentage = (recommendation.score * 100).round();

    return Row(
      children: [
        Icon(Icons.star, size: 14, color: theme.colorScheme.tertiary),
        const SizedBox(width: AppTheme.spacingXS),
        Text(
          '$scorePercentage% مطابقة',
          style: theme.textTheme.labelSmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Color _getTypeColor(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    switch (recommendation.type) {
      case RecommendationType.trending:
        return colorScheme.error;
      case RecommendationType.similarProducts:
        return Colors.blue.shade600; // Keep for variety, or map to theme
      case RecommendationType.boughtTogether:
        return Colors.green.shade600; // Keep for variety, or map to theme
      case RecommendationType.userBased:
        return colorScheme.tertiary;
      case RecommendationType.categoryBased:
        return colorScheme.secondary;
      case RecommendationType.personalized:
        return colorScheme.primary;
      default:
        return colorScheme.onSurfaceVariant;
    }
  }

  IconData _getTypeIcon() {
    // Icons remain the same
    switch (recommendation.type) {
      case RecommendationType.trending:
        return Icons.trending_up;
      case RecommendationType.similarProducts:
        return Icons.compare_arrows;
      case RecommendationType.boughtTogether:
        return Icons.shopping_cart_checkout;
      case RecommendationType.userBased:
        return Icons.group;
      case RecommendationType.categoryBased:
        return Icons.category_outlined;
      case RecommendationType.personalized:
        return Icons.person_search;
      default:
        return Icons.recommend_outlined;
    }
  }
}

/// A compact, theme-aware recommendation card, typically for horizontal lists.
class CompactRecommendationCard extends StatelessWidget {
  const CompactRecommendationCard({
    required this.recommendation,
    required this.onTap,
    super.key,
  });
  final RecommendationModel recommendation;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    return SizedBox(
      width: 160,
      child: Card(
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppTheme.radiusM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product Image
              _ProductImage(
                imageUrl: recommendation.product.images.first,
                height: 100,
              ),

              // Product Details
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(AppTheme.spacingS),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        recommendation.product.name,
                        style: textTheme.bodySmall,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const Spacer(),
                      Text(
                        '${recommendation.product.price.toStringAsFixed(2)} د.ل',
                        style: textTheme.labelLarge?.copyWith(
                          color: colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// A reusable widget for displaying the product image with a placeholder.
class _ProductImage extends StatelessWidget {
  const _ProductImage({this.imageUrl, required this.height});

  final String? imageUrl;
  final double height;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final hasImage =
        imageUrl != null && Uri.tryParse(imageUrl!)?.isAbsolute == true;

    return Container(
      height: height,
      width: double.infinity,
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainer,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(AppTheme.radiusM),
        ),
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(AppTheme.radiusM),
        ),
        child: hasImage
            ? CachedNetworkImage(
                imageUrl: imageUrl!,
                fit: BoxFit.cover,
                placeholder: (context, url) => const Center(
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                errorWidget: (context, url, error) =>
                    _buildPlaceholder(context),
              )
            : _buildPlaceholder(context),
      ),
    );
  }

  Widget _buildPlaceholder(BuildContext context) {
    final theme = Theme.of(context);
    return Center(
      child: Icon(
        Icons.photo_size_select_actual_outlined,
        size: 40,
        color: theme.colorScheme.onSurfaceVariant.withAlpha(
          (0.5 * 255).toInt(),
        ),
      ),
    );
  }
}
