import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../models/recommendation_model.dart';
import 'recommendation_card.dart';

/// ويدجت مجموعة التوصيات
class RecommendationGroupWidget extends StatelessWidget {
  const RecommendationGroupWidget({
    required this.group,
    required this.onRecommendationTap,
    super.key,
    this.onSeeAll,
    this.showSeeAll = true,
  });
  final RecommendationGroup group;
  final void Function(RecommendationModel) onRecommendationTap;
  final VoidCallback? onSeeAll;
  final bool showSeeAll;

  @override
  Widget build(BuildContext context) {
    if (!group.isVisible || group.recommendations.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _Header(group: group, onSeeAll: onSeeAll, showSeeAll: showSeeAll),
          const SizedBox(height: AppTheme.spacingM),
          _RecommendationsList(
            recommendations: group.recommendations,
            onRecommendationTap: onRecommendationTap,
          ),
        ],
      ),
    );
  }
}

/// ويدجت مجموعة التوصيات الشبكية
class GridRecommendationGroupWidget extends StatelessWidget {
  const GridRecommendationGroupWidget({
    required this.group,
    required this.onRecommendationTap,
    super.key,
    this.crossAxisCount = 2,
    this.childAspectRatio = 0.7,
  });
  final RecommendationGroup group;
  final void Function(RecommendationModel) onRecommendationTap;
  final int crossAxisCount;
  final double childAspectRatio;

  @override
  Widget build(BuildContext context) {
    if (!group.isVisible || group.recommendations.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _Header(group: group),
          const SizedBox(height: AppTheme.spacingM),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingM),
            child: GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                childAspectRatio: childAspectRatio,
                crossAxisSpacing: AppTheme.spacingM,
                mainAxisSpacing: AppTheme.spacingM,
              ),
              itemCount: group.recommendations.length,
              itemBuilder: (context, index) {
                final recommendation = group.recommendations[index];
                return RecommendationCard(
                  recommendation: recommendation,
                  onTap: () => onRecommendationTap(recommendation),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

/// A theme-aware widget for displaying featured recommendations on the home screen.
class HomeRecommendationsWidget extends StatelessWidget {
  const HomeRecommendationsWidget({
    required this.groups,
    required this.onRecommendationTap,
    super.key,
    this.onSeeAllRecommendations,
  });
  final List<RecommendationGroup> groups;
  final void Function(RecommendationModel) onRecommendationTap;
  final VoidCallback? onSeeAllRecommendations;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    if (groups.isEmpty) {
      return const SizedBox.shrink();
    }

    // Display up to 2 groups on the home screen
    final displayGroups = groups.take(2).toList();

    return Container(
      margin: const EdgeInsets.symmetric(vertical: AppTheme.spacingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingM),
            child: Row(
              children: [
                Icon(Icons.recommend, color: colorScheme.primary),
                const SizedBox(width: AppTheme.spacingS),
                Text(
                  'مقترح لك',
                  style: textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (onSeeAllRecommendations != null)
                  TextButton(
                    onPressed: onSeeAllRecommendations,
                    child: const Text('عرض المزيد'),
                  ),
              ],
            ),
          ),

          const SizedBox(height: AppTheme.spacingM),

          // Groups
          ...displayGroups.map(
            (group) => RecommendationGroupWidget(
              group: group,
              onRecommendationTap: onRecommendationTap,
              showSeeAll:
                  false, // Don't show "See All" for individual groups here
            ),
          ),
        ],
      ),
    );
  }
}

/// Private header widget for the recommendation groups.
class _Header extends StatelessWidget {
  const _Header({required this.group, this.onSeeAll, this.showSeeAll = false});

  final RecommendationGroup group;
  final VoidCallback? onSeeAll;
  final bool showSeeAll;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;
    final typeColor = _getTypeColor(context, group.type);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingM),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingS),
            decoration: BoxDecoration(
              color: typeColor.withAlpha(25),
              borderRadius: BorderRadius.circular(AppTheme.radiusM),
            ),
            child: Icon(_getTypeIcon(group.type), color: typeColor, size: 20),
          ),
          const SizedBox(width: AppTheme.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  group.title,
                  style: textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (group.subtitle.isNotEmpty) ...[
                  const SizedBox(height: 2),
                  Text(
                    group.subtitle,
                    style: textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (showSeeAll &&
              onSeeAll != null &&
              group.recommendations.length > 3)
            TextButton(onPressed: onSeeAll, child: const Text('عرض الكل')),
        ],
      ),
    );
  }
}

/// Private horizontal list for recommendations.
class _RecommendationsList extends StatelessWidget {
  const _RecommendationsList({
    required this.recommendations,
    required this.onRecommendationTap,
  });

  final List<RecommendationModel> recommendations;
  final void Function(RecommendationModel) onRecommendationTap;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 280,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingM),
        itemCount: recommendations.length,
        itemBuilder: (context, index) {
          final recommendation = recommendations[index];
          return CompactRecommendationCard(
            recommendation: recommendation,
            onTap: () => onRecommendationTap(recommendation),
          );
        },
        separatorBuilder: (context, index) =>
            const SizedBox(width: AppTheme.spacingS),
      ),
    );
  }
}

// Helper functions moved outside to be shared between widgets.
Color _getTypeColor(BuildContext context, RecommendationType type) {
  final colorScheme = Theme.of(context).colorScheme;
  switch (type) {
    case RecommendationType.trending:
      return colorScheme.error;
    case RecommendationType.similarProducts:
      return Colors.blue.shade600;
    case RecommendationType.boughtTogether:
      return Colors.green.shade600;
    case RecommendationType.userBased:
      return colorScheme.tertiary;
    case RecommendationType.categoryBased:
      return colorScheme.secondary;
    case RecommendationType.personalized:
      return colorScheme.primary;
    default:
      return colorScheme.onSurfaceVariant;
  }
}

IconData _getTypeIcon(RecommendationType type) {
  switch (type) {
    case RecommendationType.trending:
      return Icons.trending_up;
    case RecommendationType.similarProducts:
      return Icons.compare_arrows;
    case RecommendationType.boughtTogether:
      return Icons.shopping_cart_checkout;
    case RecommendationType.userBased:
      return Icons.group;
    case RecommendationType.categoryBased:
      return Icons.category_outlined;
    case RecommendationType.personalized:
      return Icons.person_search;
    default:
      return Icons.recommend_outlined;
  }
}
