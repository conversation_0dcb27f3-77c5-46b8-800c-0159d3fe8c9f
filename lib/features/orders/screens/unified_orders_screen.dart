import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../shared/widgets/app_loading_indicator.dart';
import '../../../core/widgets/app_error_widget.dart';
import '../../../shared/widgets/empty_state_widget.dart';
import '../models/models.dart';
import '../providers/unified_orders_providers.dart';

/// شاشة الطلبات الموحدة
class UnifiedOrdersScreen extends ConsumerStatefulWidget {
  const UnifiedOrdersScreen({super.key});

  @override
  ConsumerState<UnifiedOrdersScreen> createState() =>
      _UnifiedOrdersScreenState();
}

class _UnifiedOrdersScreenState extends ConsumerState<UnifiedOrdersScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  final _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الطلبات'),
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Theme.of(context).colorScheme.primary,
          labelColor: Theme.of(context).colorScheme.primary,
          unselectedLabelColor: Colors.grey,
          tabs: const [
            Tab(text: 'جميع الطلبات'),
            Tab(text: 'الطلبات النشطة'),
            Tab(text: 'الإحصائيات'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterBottomSheet(context),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshOrders,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          _buildOrderTypeChips(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllOrdersTab(),
                _buildActiveOrdersTab(),
                _buildStatisticsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCreateOrderDialog(context),
        backgroundColor: Theme.of(context).colorScheme.primary,
        child: const Icon(Icons.add),
      ),
    );
  }

  /// شريط البحث
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(color: Colors.black12, blurRadius: 4, offset: Offset(0, 2)),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث في الطلبات (رقم الطلب، اسم العميل، إلخ)',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                    _performSearch();
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.grey[100],
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
          _performSearch();
        },
        textInputAction: TextInputAction.search,
        onSubmitted: (_) => _performSearch(),
      ),
    );
  }

  /// رقائق أنواع الطلبات
  Widget _buildOrderTypeChips() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      height: 60,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: OrderType.values.length,
        itemBuilder: (context, index) {
          final orderType = OrderType.values[index];
          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Text(orderType.displayName),
              onSelected: (selected) {
                // TODO: Implement filter logic
              },
            ),
          );
        },
      ),
    );
  }

  /// تبويب جميع الطلبات
  Widget _buildAllOrdersTab() {
    final ordersAsync = ref.watch(userOrdersProvider());

    return ordersAsync.when(
      data: (orders) {
        if (orders.isEmpty) {
          return const EmptyStateWidget(
            message: 'لا توجد طلبات حتى الآن',
            icon: Icons.shopping_cart_outlined,
          );
        }

        return RefreshIndicator(
          onRefresh: _refreshOrders,
          child: ListView.separated(
            padding: const EdgeInsets.all(16),
            itemCount: orders.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              final order = orders[index];
              return _buildOrderCard(order);
            },
          ),
        );
      },
      loading: () => const AppLoadingIndicator(),
      error: (error, stackTrace) => AppErrorWidget(
        message: 'خطأ في تحميل الطلبات: ${error.toString()}',
        onRetry: _refreshOrders,
      ),
    );
  }

  /// تبويب الطلبات النشطة
  Widget _buildActiveOrdersTab() {
    final activeOrdersAsync = ref.watch(activeOrdersProvider);

    return activeOrdersAsync.when(
      data: (orders) {
        if (orders.isEmpty) {
          return const EmptyStateWidget(
            message: 'لا توجد طلبات نشطة',
            icon: Icons.hourglass_empty,
          );
        }

        return RefreshIndicator(
          onRefresh: _refreshOrders,
          child: ListView.separated(
            padding: const EdgeInsets.all(16),
            itemCount: orders.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              final order = orders[index];
              return _buildOrderCard(order);
            },
          ),
        );
      },
      loading: () => const AppLoadingIndicator(),
      error: (error, stackTrace) => AppErrorWidget(
        message: 'خطأ في تحميل الطلبات النشطة: ${error.toString()}',
        onRetry: _refreshOrders,
      ),
    );
  }

  /// تبويب الإحصائيات
  Widget _buildStatisticsTab() {
    final statsAsync = ref.watch(orderStatisticsProvider());

    return statsAsync.when(
      data: (stats) => SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatCard(
              'إجمالي الطلبات',
              '${stats['total_orders'] ?? 0}',
              Icons.shopping_cart,
              Colors.blue,
            ),
            const SizedBox(height: 16),
            _buildStatCard(
              'إجمالي المبلغ',
              '${(stats['total_amount'] ?? 0.0).toStringAsFixed(2)} ر.س',
              Icons.attach_money,
              Colors.green,
            ),
            const SizedBox(height: 16),
            _buildOrdersByTypeChart(stats),
          ],
        ),
      ),
      loading: () => const AppLoadingIndicator(),
      error: (error, stackTrace) => AppErrorWidget(
        message: 'خطأ في تحميل الإحصائيات: ${error.toString()}',
        onRetry: _refreshOrders,
      ),
    );
  }

  /// بناء كارت الطلب
  Widget _buildOrderCard(UnifiedOrder order) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _showOrderDetails(context, order),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      order.displayName,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Color(order.statusColor).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      order.status.displayName,
                      style: TextStyle(
                        color: Color(order.statusColor),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'رقم الطلب: ${order.orderNumber}',
                style: TextStyle(color: Colors.grey[600], fontSize: 14),
              ),
              const SizedBox(height: 4),
              Text(
                'النوع: ${order.orderType.displayName}',
                style: TextStyle(color: Colors.grey[600], fontSize: 14),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${order.totalAmount.toStringAsFixed(2)} ${order.currency}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Colors.green,
                    ),
                  ),
                  Text(
                    DateFormat(
                      'dd/MM/yyyy',
                    ).format(order.createdAt ?? DateTime.now()),
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء كارت الإحصائيات
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 32),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء مخطط الطلبات حسب النوع
  Widget _buildOrdersByTypeChart(Map<String, dynamic> stats) {
    final ordersByType = stats['orders_by_type'] as Map<String, int>? ?? {};

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الطلبات حسب النوع',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
            ),
            const SizedBox(height: 16),
            ...ordersByType.entries.map((entry) {
              final orderType = OrderType.values.firstWhere(
                (type) => type.name == entry.key,
                orElse: () => OrderType.general,
              );
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      orderType.displayName,
                      style: const TextStyle(fontSize: 16),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        '${entry.value}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  /// تحديث الطلبات
  Future<void> _refreshOrders() async {
    // تحديث جميع موفرات الطلبات
    ref.invalidate(userOrdersProvider);
    ref.invalidate(activeOrdersProvider);
    ref.invalidate(orderStatisticsProvider);
  }

  /// تنفيذ البحث
  void _performSearch() {
    if (_searchQuery.isNotEmpty) {
      // TODO: Implement search functionality
      // يمكن إضافة موفر للبحث لاحقاً
    }
  }

  /// عرض تفاصيل الطلب
  void _showOrderDetails(BuildContext context, UnifiedOrder order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل الطلب ${order.orderNumber}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('الاسم الوصفي', order.displayName),
              _buildDetailRow('النوع', order.orderType.displayName),
              _buildDetailRow('الحالة', order.status.displayName),
              _buildDetailRow('الأولوية', order.priority.displayName),
              _buildDetailRow(
                'المبلغ الفرعي',
                '${order.subtotal.toStringAsFixed(2)} ${order.currency}',
              ),
              _buildDetailRow(
                'الضريبة',
                '${order.taxAmount.toStringAsFixed(2)} ${order.currency}',
              ),
              _buildDetailRow(
                'الشحن',
                '${order.shippingCost.toStringAsFixed(2)} ${order.currency}',
              ),
              _buildDetailRow(
                'المبلغ الإجمالي',
                '${order.totalAmount.toStringAsFixed(2)} ${order.currency}',
              ),
              _buildDetailRow(
                'تاريخ الإنشاء',
                DateFormat(
                  'dd/MM/yyyy HH:mm',
                ).format(order.createdAt ?? DateTime.now()),
              ),
              if (order.notes?.isNotEmpty == true)
                _buildDetailRow('ملاحظات', order.notes!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          // if (order.canBeUpdated)  // TODO: Add canBeUpdated getter to UnifiedOrder
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showUpdateStatusDialog(context, order);
            },
            child: const Text('تحديث الحالة'),
          ),
        ],
      ),
    );
  }

  /// بناء صف التفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  /// عرض حوار تحديث الحالة
  void _showUpdateStatusDialog(BuildContext context, UnifiedOrder order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحديث حالة الطلب'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: OrderStatus.values.map((status) {
            return RadioListTile<OrderStatus>(
              title: Text(status.displayName),
              value: status,
              groupValue: order.status,
              onChanged: (value) {
                if (value != null) {
                  Navigator.of(context).pop();
                  _updateOrderStatus(order.id, value);
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// تحديث حالة الطلب
  Future<void> _updateOrderStatus(String orderId, OrderStatus newStatus) async {
    try {
      // TODO: Implement actual order status update
      // await ref.read(updateOrderStatusProvider(...).future);

      if (!context.mounted) return;
      // ignore: use_build_context_synchronously
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تحديث حالة الطلب بنجاح'),
          backgroundColor: Colors.green,
        ),
      );

      await _refreshOrders();
    } catch (e) {
      if (!context.mounted) return;
      // ignore: use_build_context_synchronously
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحديث حالة الطلب: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// عرض حوار إنشاء طلب جديد
  void _showCreateOrderDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إنشاء طلب جديد'),
        content: const Text('سيتم إضافة نموذج إنشاء الطلبات لاحقاً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض ورقة الفلاتر
  void _showFilterBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تصفية الطلبات',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            const Text('سيتم إضافة خيارات التصفية لاحقاً'),
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: FilledButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إغلاق'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
