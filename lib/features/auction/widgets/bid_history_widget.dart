import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../core/widgets/app_error_widget.dart';
import '../../../core/widgets/loading_indicators.dart';
import '../../../core/theme/app_color_extensions.dart';
import '../../../shared/utils/app_styles.dart';
import '../models/bid_model.dart';
import '../providers/auction_provider.dart';

/// Widget to display bid history for a specific part
class BidHistoryWidget extends ConsumerWidget {
  const BidHistoryWidget({
    required this.partId,
    super.key,
    this.showOnlyUserBids = false,
    this.userId,
  });
  final String partId;
  final bool showOnlyUserBids;
  final String? userId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bidsAsync = ref.watch(auctionBidsProvider(partId));

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha((05 * 255).toInt()),
            spreadRadius: 2,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              showOnlyUserBids ? 'عطاءاتي' : 'تاريخ العطاءات',
              style: AppStyles.headingSmall.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          const Divider(height: 1),
          Expanded(
            child: bidsAsync.when(
              data: (bids) {
                var filteredBids = bids;
                if (showOnlyUserBids && userId != null) {
                  filteredBids = bids
                      .where((bid) => bid.userId == userId)
                      .toList();
                }

                if (filteredBids.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          showOnlyUserBids
                              ? Icons.gavel_outlined
                              : Icons.history_outlined,
                          size: 64,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          showOnlyUserBids
                              ? 'لم تقم بوضع أي عطاءات بعد'
                              : 'لا توجد عطاءات حتى الآن',
                          style: AppStyles.bodyLarge(context).copyWith(
                            color: Colors.grey.shade600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        if (showOnlyUserBids) ...[
                          const SizedBox(height: 8),
                          Text(
                            'كن أول من يضع عطاء على هذا المنتج',
                            style: AppStyles.bodyMedium(context).copyWith(
                              color: Colors.grey.shade500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ],
                    ),
                  );
                }

                return RefreshIndicator(
                  onRefresh: () async {
                    ref.invalidate(auctionBidsProvider(partId));
                  },
                  child: ListView.separated(
                    padding: const EdgeInsets.all(16),
                    itemCount: filteredBids.length,
                    separatorBuilder: (context, index) =>
                        const SizedBox(height: 12),
                    itemBuilder: (context, index) {
                      final bid = filteredBids[index];
                      final isHighestBid = index == 0;
                      final isUserBid = bid.userId == userId;

                      return _BidItem(
                        bid: bid,
                        isHighestBid: isHighestBid,
                        isUserBid: isUserBid,
                        rank: index + 1,
                      );
                    },
                  ),
                );
              },
              loading: () => const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: LoadingSpinner(),
                ),
              ),
              error: (error, stackTrace) => Center(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: AppErrorWidget(
                    message: 'خطأ في تحميل العطاءات',
                    details: error.toString(),
                    stackTrace: stackTrace,
                    onRetry: () {
                      ref.invalidate(auctionBidsProvider(partId));
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _BidItem extends StatelessWidget {
  const _BidItem({
    required this.bid,
    required this.isHighestBid,
    required this.isUserBid,
    required this.rank,
  });
  final BidModel bid;
  final bool isHighestBid;
  final bool isUserBid;
  final int rank;

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm', 'ar');

    return Container(
      decoration: BoxDecoration(
        color: isHighestBid
            ? context.colors.success.withAlpha((0.20 * 255).toInt())
            : (isUserBid
                  ? Theme.of(context).colorScheme.primary.withAlpha((0.10 * 255).toInt())
                  : Colors.grey.shade50),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isHighestBid
              ? context.colors.success.withAlpha((08 * 255).toInt())
              : isUserBid
              ? Theme.of(context).colorScheme.primary.withAlpha((0.10 * 255).toInt())
              : Colors.grey.shade200,
          width: isHighestBid || isUserBid ? 2 : 1,
        ),
      ),
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Rank badge
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: isHighestBid
                  ? context.colors.success.withAlpha((0.20 * 255).toInt())
                  : isUserBid
                  ? Theme.of(context).colorScheme.primary.withAlpha((0.10 * 255).toInt())
                  : Colors.grey.shade400,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: isHighestBid
                  ? const Icon(
                      Icons.emoji_events,
                      color: Colors.white,
                      size: 18,
                    )
                  : Text(
                      rank.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
            ),
          ),
          const SizedBox(width: 12),

          // Bid details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      '${(bid.amount ?? 0).toStringAsFixed(0)} د.ل',
                      style: AppStyles.headingSmall.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isHighestBid
                            ? context.colors.success
                            : isUserBid
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    if (isHighestBid) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: context.colors.success.withAlpha(
                            (0.20 * 255).toInt(),
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'الأعلى',
                          style: AppStyles.caption(context).copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                    if (isUserBid && !isHighestBid) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary.withAlpha(
                            (0.10 * 255).toInt(),
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'عطائي',
                          style: AppStyles.caption(context).copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  bid.createdAt != null
                      ? dateFormat.format(bid.createdAt!)
                      : 'غير محدد',
                  style: AppStyles.bodySmall(context).copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
