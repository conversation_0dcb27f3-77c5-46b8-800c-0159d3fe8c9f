/// شاشة قائمة المزادات
///
/// تعرض قائمة بجميع المزادات النشطة حاليًا في التطبيق.
/// تتيح للمستخدم استعراض المزادات المتاحة، مع توفير أدوات قوية
/// للبحث عن مزادات معينة، وتصفية النتائج حسب الفئة أو السعر،
/// وترتيبها حسب وقت الانتهاء أو السعر.
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../core/widgets/loading_indicators.dart';
import '../../../core/theme/app_color_extensions.dart';
import '../../../shared/utils/app_styles.dart';
import '../providers/auction_provider.dart';
import '../widgets/auction_timer_widget.dart';
import '../widgets/bid_stats_widget.dart';
import '../../../core/widgets/unified_app_bar.dart';
import '../../../l10n/app_localizations.dart';
import '../auction_localizations_extensions.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../core/widgets/app_error_widget.dart';

class AuctionListScreen extends ConsumerStatefulWidget {
  const AuctionListScreen({super.key});

  @override
  ConsumerState<AuctionListScreen> createState() => _AuctionListScreenState();
}

class _AuctionListScreenState extends ConsumerState<AuctionListScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _sortBy = 'ending_soon';
  String _filterBy = 'all';
  bool _showActiveOnly = true;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    // Using the auction parts provider
    final auctionPartsAsync = ref.watch(auctionsProvider);

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: UnifiedAppBar(
        title: localizations.auctions,
        actions: [
          IconButton(
            icon: const Icon(Icons.tune),
            onPressed: _showFiltersBottomSheet,
            tooltip: 'الفلاتر',
          ),
          IconButton(
            icon: const Icon(Icons.my_library_books),
            onPressed: () => context.push('/my-bids'),
            tooltip: 'عطاءاتي',
          ),
        ],
      ),
      body: Column(
        children: [
          Container(
            color: Colors.white,
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                CustomTextField(
                  controller: _searchController,
                  hint: 'البحث في المزادات (اسم القطعة، الماركة، ...)',
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value.toLowerCase();
                    });
                  },
                  prefixIcon: Icons.search,
                  suffixIcon: _searchController.text.isNotEmpty ? Icons.clear : null,
                  onSuffixIconPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                ),
                const SizedBox(height: 12),
                _buildQuickFilters(),
              ],
            ),
          ),
          Expanded(
            child: auctionPartsAsync.when(
              data: (parts) {
                final filteredParts = _filterAndSortParts(parts);

                if (filteredParts.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.gavel_outlined,
                          size: 64,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _searchQuery.isNotEmpty
                              ? localizations.noAuctionsFound
                              : localizations.noAuctionsAvailable,
                          style: AppStyles.bodyLarge(context).copyWith(
                            color: Colors.grey.shade600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                }

                return RefreshIndicator(
                  onRefresh: () async {
                    ref.invalidate(auctionsProvider);
                  },
                  child: ListView.separated(
                    padding: const EdgeInsets.all(16),
                    itemCount: filteredParts.length,
                    separatorBuilder: (context, index) =>
                        const SizedBox(height: 16),
                    itemBuilder: (context, index) {
                      final part = filteredParts[index];
                      return AuctionCard(
                        part: part,
                        onTap: () => context.push('/part/${part.id}'),
                      );
                    },
                  ),
                );
              },
              loading: () => const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: LoadingSpinner(),
                ),
              ),
              error: (error, stackTrace) => Center(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: AppErrorWidget(
                    message: localizations.errorLoadingAuctions(
                      error.toString(),
                    ),
                    details: error.toString(),
                    stackTrace: stackTrace,
                    onRetry: () {
                      ref.invalidate(auctionsProvider);
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickFilters() => SingleChildScrollView(
    scrollDirection: Axis.horizontal,
    child: Row(
      children: [
        FilterChip(
          label: const Text('الكل'),
          selected: _filterBy == 'all',
          onSelected: (selected) {
            setState(() => _filterBy = 'all');
          },
        ),
        const SizedBox(width: 8),
        FilterChip(
          label: const Text('قطع غيار'),
          selected: _filterBy == 'parts',
          onSelected: (selected) {
            setState(() => _filterBy = 'parts');
          },
        ),
        const SizedBox(width: 8),
        FilterChip(
          label: const Text('زيوت'),
          selected: _filterBy == 'oils',
          onSelected: (selected) {
            setState(() => _filterBy = 'oils');
          },
        ),
        const SizedBox(width: 8),
        FilterChip(
          label: const Text('إكسسوارات'),
          selected: _filterBy == 'accessories',
          onSelected: (selected) {
            setState(() => _filterBy = 'accessories');
          },
        ),
        const SizedBox(width: 8),
        FilterChip(
          label: const Text('نشط فقط'),
          selected: _showActiveOnly,
          onSelected: (selected) {
            setState(() => _showActiveOnly = selected);
          },
        ),
      ],
    ),
  );

  void _showFiltersBottomSheet() {
    showModalBottomSheet<Widget>(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'فلاتر البحث',
                  style: AppStyles.headingMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Text(
              'ترتيب حسب',
              style: AppStyles.bodyLarge(context).copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            Column(
              children: [
                RadioListTile<String>(
                  title: const Text('انتهاء المزاد قريباً'),
                  value: 'ending_soon',
                  groupValue: _sortBy,
                  onChanged: (value) {
                    setState(() => _sortBy = value!);
                    Navigator.pop(context);
                  },
                ),
                RadioListTile<String>(
                  title: const Text('أعلى عطاء'),
                  value: 'highest_bid',
                  groupValue: _sortBy,
                  onChanged: (value) {
                    setState(() => _sortBy = value!);
                    Navigator.pop(context);
                  },
                ),
                RadioListTile<String>(
                  title: const Text('أكثر عطاءات'),
                  value: 'most_bids',
                  groupValue: _sortBy,
                  onChanged: (value) {
                    setState(() => _sortBy = value!);
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
            SizedBox(height: MediaQuery.of(context).padding.bottom),
          ],
        ),
      ),
    );
  }

  List<dynamic> _filterAndSortParts(List<dynamic> parts) {
    try {
      // Filter by search query
      var filtered = parts.where((part) {
        final searchLower = _searchQuery.toLowerCase();
        final nameMatch = (part.name as String? ?? '').toLowerCase().contains(
          searchLower,
        );
        final brandMatch =
            (part.brand as String?)?.toLowerCase().contains(searchLower) ??
            false;
        final modelMatch =
            (part.carModel as String?)?.toLowerCase().contains(searchLower) ??
            false;
        final descMatch =
            (part.description as String?)?.toLowerCase().contains(
              searchLower,
            ) ??
            false;

        return nameMatch || brandMatch || modelMatch || descMatch;
      }).toList();

      // Apply category filter
      if (_filterBy != 'all') {
        filtered = filtered
            .where((part) => part.category == _filterBy)
            .toList();
      }

      // Apply active filter
      if (_showActiveOnly) {
        filtered = filtered.where((part) {
          final endDate = part.auctionEndDate as DateTime?;
          return endDate != null && DateTime.now().isBefore(endDate);
        }).toList();
      }

      // Sort results
      filtered.sort((a, b) {
        if (_sortBy == 'ending_soon') {
          final defaultDate = DateTime.now().add(const Duration(days: 365));
          final aEnd = (a.auctionEndDate as DateTime?) ?? defaultDate;
          final bEnd = (b.auctionEndDate as DateTime?) ?? defaultDate;
          return aEnd.compareTo(bEnd);
        } else if (_sortBy == 'newest') {
          return (b.createdAt as DateTime).compareTo(a.createdAt as DateTime);
        } else if (_sortBy == 'highest_bid') {
          final aBid = (a.currentBid as num?) ?? 0;
          final bBid = (b.currentBid as num?) ?? 0;
          return bBid.compareTo(aBid);
        } else if (_sortBy == 'lowest_bid') {
          final aBid = (a.currentBid as num?) ?? double.maxFinite;
          final bBid = (b.currentBid as num?) ?? double.maxFinite;
          return aBid.compareTo(bBid);
        }
        return 0;
      });

      return filtered;
    } catch (e) {
      debugPrint('Error filtering parts: $e');
      return [];
    }
  }
}

class AuctionCard extends ConsumerWidget {
  const AuctionCard({required this.part, super.key, this.onTap});
  final dynamic part;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bidsAsync = ref.watch(auctionBidsProvider(part.id.toString()));
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm', 'ar');

    final isActive =
        part.auctionEndDate != null &&
        DateTime.now().isBefore(part.auctionEndDate as DateTime);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isActive
              ? context.colors.success.withAlpha((0.30 * 255).toInt())
              : Colors.grey.withAlpha((0.30 * 255).toInt()),
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: isActive
                          ? context.colors.success.withAlpha((0.10 * 255).toInt())
                          : Colors.grey.withAlpha((0.10 * 255).toInt()),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      isActive ? 'نشط' : 'منتهي',
                      style: AppStyles.caption(context).copyWith(
                        color: isActive
                            ? context.colors.success
                            : Colors.grey.shade600,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const Spacer(),
                  if (part.auctionEndDate != null)
                    AuctionTimerWidget(
                      auctionEndDate: part.auctionEndDate as DateTime,
                    ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                part.name as String,
                style: AppStyles.headingSmall.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              if (part.brand != null || part.carModel != null) ...[
                const SizedBox(height: 4),
                Text(
                  '${part.brand ?? ''} ${part.carModel ?? ''}'.trim(),
                  style: AppStyles.bodyMedium(context).copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: bidsAsync.when(
                      data: (bids) {
                        if (bids.isEmpty) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'السعر الابتدائي',
                                style: AppStyles.caption(context).copyWith(
                                  color: Colors.grey.shade600,
                                ),
                              ),
                              Text(
                                '${part.startingPrice?.toStringAsFixed(0) ?? '0'} '
                                'د.ل',
                                style: AppStyles.headingSmall.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                              ),
                            ],
                          );
                        }

                        final currentBid = bids.first.amount;
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'العطاء الحالي',
                              style: AppStyles.caption(context).copyWith(
                                color: Colors.grey.shade600,
                              ),
                            ),
                            Text(
                              '${currentBid?.toStringAsFixed(0) ?? '0'} د.ل',
                              style: AppStyles.headingSmall.copyWith(
                                fontWeight: FontWeight.bold,
                                color: context.colors.success,
                              ),
                            ),
                          ],
                        );
                      },
                      loading: () => const CircularProgressIndicator(),
                      error: (_, _) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'السعر الابتدائي',
                            style: AppStyles.caption(context).copyWith(
                              color: Colors.grey.shade600,
                            ),
                          ),
                          Text(
                            '${part.startingPrice?.toStringAsFixed(0) ?? '0'} '
                            'د.ل',
                            style: AppStyles.headingSmall.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Bid count
                  bidsAsync.when(
                    data: (bids) => BidStatsWidget(
                      partId: part.id.toString(),
                      isCompact: true,
                    ),
                    loading: () => const SizedBox.shrink(),
                    error: (_, _) => const SizedBox.shrink(),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              if (part.auctionEndDate != null)
                Text(
                  'ينتهي: '
                  '${dateFormat.format(part.auctionEndDate as DateTime)}',
                  style: AppStyles.caption(context).copyWith(
                    color: Colors.grey.shade500,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
