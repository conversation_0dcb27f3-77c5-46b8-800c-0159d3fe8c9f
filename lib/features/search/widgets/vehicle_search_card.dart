import 'package:flutter/material.dart';

class VehicleSearchCard extends StatelessWidget {
  const VehicleSearchCard({required this.vehicle, super.key, this.onTap});
  final Map<String, dynamic> vehicle;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final make = vehicle['vehicle_makes'] as Map<String, dynamic>?;
    final type = vehicle['vehicle_types'] as Map<String, dynamic>?;
    final specifications = vehicle['specifications'] as Map<String, dynamic>?;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with make and model
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Theme.of(
                        context,
                      ).colorScheme.primary.withAlpha((0.1 * 255).toInt()),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.directions_car,
                      color: Theme.of(context).colorScheme.primary,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${make?['name'] ?? 'غير محدد'} ${vehicle['name']}',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.flag,
                              size: 14,
                              color: Colors.grey.shade600,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              make?['country'] as String? ?? 'غير محدد',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: Colors.grey.shade600),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  if (make?['is_commercial'] == true)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.orange.withAlpha((0.1 * 255).toInt()),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'تجاري',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.orange.shade700,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 16),

              // Vehicle details
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    _buildDetailRow(
                      context,
                      'النوع',
                      type?['name'] as String? ?? 'غير محدد',
                      Icons.category,
                    ),
                    const SizedBox(height: 8),
                    _buildDetailRow(
                      context,
                      'السنوات',
                      '${vehicle['year_from']} - ${vehicle['year_to']}',
                      Icons.calendar_today,
                    ),
                    if (specifications != null) ...[
                      const SizedBox(height: 8),
                      _buildDetailRow(
                        context,
                        'نوع الوقود',
                        specifications['fuel_type'] as String? ?? 'غير محدد',
                        Icons.local_gas_station,
                      ),
                      if (specifications['engine'] != null) ...[
                        const SizedBox(height: 8),
                        _buildDetailRow(
                          context,
                          'المحرك',
                          specifications['engine'] as String,
                          Icons.settings,
                        ),
                      ],
                      if (specifications['transmission'] != null) ...[
                        const SizedBox(height: 8),
                        _buildDetailRow(
                          context,
                          'ناقل الحركة',
                          specifications['transmission'] as String,
                          Icons.sync,
                        ),
                      ],
                    ],
                  ],
                ),
              ),

              // Popular in Libya indicator
              if (specifications?['popular_in_libya'] == true) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.withAlpha((0.1 * 255).toInt()),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.green.withAlpha((0.3 * 255).toInt()),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.star, size: 16, color: Colors.green[700]),
                      const SizedBox(width: 4),
                      Text(
                        'شائع في ليبيا',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.green[700],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 8),

              // Action button
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    onPressed: onTap,
                    icon: const Icon(Icons.info_outline, size: 16),
                    label: const Text('عرض التفاصيل'),
                    style: TextButton.styleFrom(
                      foregroundColor: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) => Row(
    children: [
      Icon(icon, size: 16, color: Colors.grey.shade600),
      const SizedBox(width: 8),
      Text(
        '$label:',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Colors.grey.shade600,
          fontWeight: FontWeight.w500,
        ),
      ),
      const SizedBox(width: 8),
      Expanded(
        child: Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w600),
        ),
      ),
    ],
  );
}
