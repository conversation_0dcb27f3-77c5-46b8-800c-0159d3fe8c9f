import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// ويدجت فلاتر البحث المتقدمة
class AdvancedSearchFiltersWidget extends HookConsumerWidget {
  const AdvancedSearchFiltersWidget({
    required this.onFiltersChanged,
    super.key,
    this.initialFilters = const {},
  });
  final void Function(Map<String, dynamic>) onFiltersChanged;
  final Map<String, dynamic> initialFilters;

  @override
  Widget build(BuildContext context, WidgetRef ref) => Container(
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: Theme.of(context).cardColor,
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withAlpha((0.1 * 255).toInt()),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(context),
        const SizedBox(height: 16),
        _buildVehicleSpecsSection(context, ref),
        const SizedBox(height: 16),
        _buildPriceRangeSection(context),
        const SizedBox(height: 16),
        _buildConditionSection(context),
        const SizedBox(height: 16),
        _buildSafetyFeaturesSection(context, ref),
        const SizedBox(height: 20),
        _buildActionButtons(context),
      ],
    ),
  );

  Widget _buildHeader(BuildContext context) => Row(
    children: [
      Icon(Icons.tune, color: Theme.of(context).primaryColor, size: 24),
      const SizedBox(width: 8),
      Text(
        'فلاتر البحث المتقدمة',
        style: Theme.of(
          context,
        ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
      ),
      const Spacer(),
      TextButton(onPressed: _clearAllFilters, child: const Text('مسح الكل')),
    ],
  );

  Widget _buildVehicleSpecsSection(BuildContext context, WidgetRef ref) =>
      ExpansionTile(
        title: const Text('مواصفات المركبة'),
        leading: const Icon(Icons.directions_car),
        children: [
          _buildBodyStyleFilter(context, ref),
          const SizedBox(height: 12),
          _buildTransmissionFilter(context, ref),
          const SizedBox(height: 12),
          _buildBrakeSystemFilter(context, ref),
          const SizedBox(height: 12),
          _buildTurboFilter(context),
        ],
      );

  Widget _buildBodyStyleFilter(BuildContext context, WidgetRef ref) =>
      FutureBuilder<List<Map<String, dynamic>>>(
        future: _getBodyStyles(ref),
        builder: (context, snapshot) {
          if (!snapshot.hasData) {
            return const CircularProgressIndicator();
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'نوع هيكل السيارة',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: snapshot.data!.map((bodyStyle) {
                  final isSelected =
                      initialFilters['body_style_id'] == bodyStyle['id'];
                  return FilterChip(
                    label: Text(bodyStyle['name'] as String),
                    selected: isSelected,
                    onSelected: (selected) {
                      _updateFilter(
                        'body_style_id',
                        selected ? bodyStyle['id'] : null,
                      );
                    },
                  );
                }).toList(),
              ),
            ],
          );
        },
      );

  Widget _buildTransmissionFilter(BuildContext context, WidgetRef ref) =>
      FutureBuilder<List<Map<String, dynamic>>>(
        future: _getTransmissionTypes(ref),
        builder: (context, snapshot) {
          if (!snapshot.hasData) {
            return const CircularProgressIndicator();
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'نوع ناقل الحركة',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: snapshot.data!.map((transmission) {
                  final isSelected =
                      initialFilters['transmission_type_id'] ==
                      transmission['id'];
                  return FilterChip(
                    label: Text(transmission['name'] as String),
                    selected: isSelected,
                    onSelected: (selected) {
                      _updateFilter(
                        'transmission_type_id',
                        selected ? transmission['id'] : null,
                      );
                    },
                  );
                }).toList(),
              ),
            ],
          );
        },
      );

  Widget _buildBrakeSystemFilter(BuildContext context, WidgetRef ref) =>
      FutureBuilder<List<Map<String, dynamic>>>(
        future: _getBrakeSystems(ref),
        builder: (context, snapshot) {
          if (!snapshot.hasData) {
            return const CircularProgressIndicator();
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'نظام الفرامل',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: snapshot.data!.map((brakeSystem) {
                  final isSelected =
                      initialFilters['brake_system_id'] == brakeSystem['id'];
                  return FilterChip(
                    label: Text(brakeSystem['name'] as String),
                    selected: isSelected,
                    onSelected: (selected) {
                      _updateFilter(
                        'brake_system_id',
                        selected ? brakeSystem['id']?.toString() : null,
                      );
                    },
                  );
                }).toList(),
              ),
            ],
          );
        },
      );

  Widget _buildTurboFilter(BuildContext context) => Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      const Text('التوربو', style: TextStyle(fontWeight: FontWeight.w600)),
      const SizedBox(height: 8),
      Row(
        children: [
          FilterChip(
            label: const Text('مع توربو'),
            selected: initialFilters['has_turbo'] == true,
            onSelected: (selected) {
              _updateFilter('has_turbo', selected ? true : null);
            },
          ),
          const SizedBox(width: 8),
          FilterChip(
            label: const Text('بدون توربو'),
            selected: initialFilters['has_turbo'] == false,
            onSelected: (selected) {
              _updateFilter('has_turbo', selected ? false : null);
            },
          ),
        ],
      ),
    ],
  );

  Widget _buildPriceRangeSection(BuildContext context) => ExpansionTile(
    title: const Text('نطاق السعر'),
    leading: const Icon(Icons.attach_money),
    children: [
      Row(
        children: [
          Expanded(
            child: TextFormField(
              decoration: const InputDecoration(
                labelText: 'السعر الأدنى',
                prefixText: 'LYD ',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                final price = double.tryParse(value);
                _updateFilter('price_min', price);
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: TextFormField(
              decoration: const InputDecoration(
                labelText: 'السعر الأعلى',
                prefixText: 'LYD ',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                final price = double.tryParse(value);
                _updateFilter('price_max', price);
              },
            ),
          ),
        ],
      ),
    ],
  );

  Widget _buildConditionSection(BuildContext context) => ExpansionTile(
    title: const Text('حالة القطعة'),
    leading: const Icon(Icons.star),
    children: [
      Wrap(
        spacing: 8,
        runSpacing: 8,
        children: ['جديد', 'مستعمل - ممتاز', 'مستعمل - جيد', 'مستعمل - مقبول']
            .map((condition) {
              final isSelected = initialFilters['condition'] == condition;
              return FilterChip(
                label: Text(condition),
                selected: isSelected,
                onSelected: (selected) {
                  _updateFilter('condition', selected ? condition : null);
                },
              );
            })
            .toList(),
      ),
    ],
  );

  Widget _buildSafetyFeaturesSection(BuildContext context, WidgetRef ref) =>
      ExpansionTile(
        title: const Text('ميزات الأمان'),
        leading: const Icon(Icons.security),
        children: [
          CheckboxListTile(
            title: const Text('مراقبة النقطة العمياء'),
            value: initialFilters['blind_spot_monitoring'] == true,
            onChanged: (value) {
              _updateFilter('blind_spot_monitoring', value);
            },
          ),
          CheckboxListTile(
            title: const Text('تدخل النقطة العمياء'),
            value: initialFilters['blind_spot_intervention'] == true,
            onChanged: (value) {
              _updateFilter('blind_spot_intervention', value);
            },
          ),
        ],
      );

  Widget _buildActionButtons(BuildContext context) => Row(
    children: [
      Expanded(
        child: OutlinedButton(
          onPressed: _clearAllFilters,
          child: const Text('مسح الفلاتر'),
        ),
      ),
      const SizedBox(width: 16),
      Expanded(
        child: FilledButton(
          onPressed: _applyFilters,
          child: const Text('تطبيق الفلاتر'),
        ),
      ),
    ],
  );

  // Helper methods - Forever Plan Architecture
  // ✅ Uses asset-based data instead of direct Supabase calls
  Future<List<Map<String, dynamic>>> _getBodyStyles(WidgetRef ref) async {
    try {
      final String response = await rootBundle.loadString('assets/data/body_styles.json');
      final List<dynamic> data = json.decode(response);
      return data.cast<Map<String, dynamic>>();
    } catch (e) {
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> _getTransmissionTypes(
    WidgetRef ref,
  ) async {
    try {
      final String response = await rootBundle.loadString('assets/data/transmission_types.json');
      final List<dynamic> data = json.decode(response);
      return data.cast<Map<String, dynamic>>();
    } catch (e) {
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> _getBrakeSystems(WidgetRef ref) async {
    try {
      final String response = await rootBundle.loadString('assets/data/brake_systems.json');
      final List<dynamic> data = json.decode(response);
      return data.cast<Map<String, dynamic>>();
    } catch (e) {
      return [];
    }
  }

  void _updateFilter(String key, dynamic value) {
    final updatedFilters = Map<String, dynamic>.from(initialFilters);
    if (value == null) {
      updatedFilters.remove(key);
    } else {
      updatedFilters[key] = value;
    }
    onFiltersChanged(updatedFilters);
  }

  void _clearAllFilters() {
    onFiltersChanged({});
  }

  void _applyFilters() {
    onFiltersChanged(initialFilters);
  }
}

/// ويدجت الاقتراحات الذكية
class SmartSuggestionsWidget extends HookConsumerWidget {
  const SmartSuggestionsWidget({
    required this.onSuggestionSelected,
    super.key,
    this.userVehicleId,
  });
  final String? userVehicleId;
  final void Function(Map<String, dynamic>) onSuggestionSelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) =>
      FutureBuilder<List<Map<String, dynamic>>>(
        future: _getSmartSuggestions(ref),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const SizedBox.shrink();
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'اقتراحات ذكية',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ),
              SizedBox(
                height: 120,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: snapshot.data!.length,
                  itemBuilder: (context, index) {
                    final suggestion = snapshot.data![index];
                    return _buildSuggestionCard(context, suggestion);
                  },
                ),
              ),
            ],
          );
        },
      );

  Widget _buildSuggestionCard(
    BuildContext context,
    Map<String, dynamic> suggestion,
  ) => Container(
    width: 200,
    margin: const EdgeInsets.only(right: 12),
    child: Card(
      child: InkWell(
        onTap: () => onSuggestionSelected(suggestion['filters'] as Map<String, dynamic>),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    _getIconData(suggestion['icon'] as String?),
                    color: Theme.of(context).primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      suggestion['title'] as String,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                suggestion['description'] as String,
                style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const Spacer(),
              Text(
                '${suggestion['estimated_results']} نتيجة متوقعة',
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );

  IconData _getIconData(String? iconName) {
    switch (iconName) {
      case 'car_repair':
        return Icons.car_repair;
      case 'directions_car':
        return Icons.directions_car;
      case 'sports_car':
        return Icons.sports;
      case 'local_shipping':
        return Icons.local_shipping;
      default:
        return Icons.auto_fix_high;
    }
  }

  Future<List<Map<String, dynamic>>> _getSmartSuggestions(WidgetRef ref) async {
    // اقتراحات ثابتة للتجربة
    return [
      {
        'title': 'قطع غيار للسيارات الرياضية',
        'description': 'قطع غيار محسنة للأداء العالي',
        'icon': 'sports_car',
        'estimated_results': 25,
        'filters': {'body_style_id': 3, 'category': 'performance'},
      },
      {
        'title': 'قطع غيار للشاحنات',
        'description': 'قطع غيار متينة للاستخدام التجاري',
        'icon': 'local_shipping',
        'estimated_results': 40,
        'filters': {'body_style_id': 60, 'category': 'heavy_duty'},
      },
      {
        'title': 'قطع غيار كهربائية',
        'description': 'قطع غيار للسيارات الكهربائية',
        'icon': 'car_repair',
        'estimated_results': 15,
        'filters': {'category': 'electric'},
      },
    ];
  }
}
