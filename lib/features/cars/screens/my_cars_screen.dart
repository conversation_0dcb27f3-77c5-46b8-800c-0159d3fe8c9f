import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../l10n/app_localizations.dart';
import '../../../shared/widgets/empty_state_widget.dart';
import '../../../shared/widgets/error_message.dart';
import '../../../shared/widgets/loading_indicator.dart';
import '../providers/cars_provider.dart';

/// شاشة "سياراتي"
///
/// تعرض قائمة بالمركبات التي أضافها المستخدم إلى حسابه.
/// تتيح للمستخدم استعراض سياراته، إضافة سيارة جديدة، والانتقال
/// إلى تفاصيل كل سيارة.
/// تعالج الشاشة حالات التحميل، الخطأ، والحالة الفارغة (عدم وجود سيارات).
class MyCarsScreen extends HookConsumerWidget {
  const MyCarsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    // استخدام المزود مع معالجة حالات AsyncValue
    final carsAsync = ref.watch(userCarsProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.myCars),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              // التنقل إلى شاشة إضافة سيارة
              context.go('/add-car');
            },
          ),
        ],
      ),
      body: carsAsync.when(
        data: (cars) {
          // عرض رسالة الحالة الفارغة إذا لم يكن هناك سيارات
          if (cars.isEmpty) {
            return EmptyStateWidget(
              icon: Icons.directions_car_outlined,
              message: 'لا توجد سيارات',
              action: () {
                // التنقل إلى شاشة إضافة سيارة
                context.go('/add-car');
              },
              actionLabel: 'إضافة سيارة',
            );
          }

          // عرض قائمة السيارات باستخدام ListView.builder للأداء الأفضل
          return RefreshIndicator(
            onRefresh: () async {
              // إعادة تحميل البيانات
              ref.invalidate(userCarsProvider);
            },
            child: ListView.builder(
              // استخدام cacheExtent لتحسين أداء التمرير
              cacheExtent: 120,
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(8),
              itemCount: cars.length,
              itemBuilder: (context, index) {
                final car = cars[index];
                // استخدام static حيثما أمكن لتحسين الأداء
                return Card(
                  elevation: 2,
                  margin: const EdgeInsets.only(bottom: 12),
                  child: ListTile(
                    leading: const Icon(Icons.directions_car, size: 40),
                    title: Text(
                      '${car.make} ${car.model}',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    subtitle: Text(
                      '${car.year}'
                      '${car.color != null ? ' • ${car.color}' : ''}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () {
                      // التنقل إلى تفاصيل السيارة
                      // يمكن إضافتها لاحقًا
                    },
                  ),
                );
              },
            ),
          );
        },
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ErrorMessage(message: error.toString()),
              const SizedBox(height: 16),
              FilledButton(
                onPressed: () => ref.invalidate(userCarsProvider),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
        loading: () => const Center(
          // استخدام مؤشر تحميل موحد
          child: LoadingIndicator(),
        ),
      ),
    );
  }
}
