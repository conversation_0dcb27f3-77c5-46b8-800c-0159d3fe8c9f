import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/providers/admin_provider.dart';
import '../providers/admin_management_provider.dart';

/// Widget إعدادات المشرف
class AdminSettingsWidget extends ConsumerWidget {
  const AdminSettingsWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAdmin = ref.watch(isAdminProvider);

    if (!isAdmin) {
      return const SizedBox.shrink();
    }

    return Card(
          margin: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان القسم
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withAlpha((0.1 * 255).toInt()),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.admin_panel_settings, color: Theme.of(context).colorScheme.primary),
                    const SizedBox(width: 8),
                    Text(
                      'إعدادات المشرف',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ),

              // قائمة الإعدادات
              Column(
                children: [
                  // إدارة المشرفين
                  _buildSettingTile(
                    context: context,
                    title: 'إدارة المشرفين',
                    subtitle: 'تعيين وإزالة المشرفين',
                    icon: Icons.people,
                    onTap: () => context.go('/admin/manage-admins'),
                  ),

                  // إدارة المستخدمين
                  _buildSettingTile(
                    context: context,
                    title: 'إدارة المستخدمين',
                    subtitle: 'عرض وإدارة جميع المستخدمين',
                    icon: Icons.supervised_user_circle,
                    onTap: () => context.go('/admin/users'),
                  ),

                  // إدارة المركبات (إذا كان متاحاً)
                  _buildSettingTile(
                    context: context,
                    title: 'إدارة المركبات',
                    subtitle: 'إضافة وتحرير بيانات المركبات',
                    icon: Icons.directions_car,
                    onTap: () => context.go('/developer/additions/add-car'),
                  ),

                  // إدارة المال والمحافظ
                  _buildSettingTile(
                    context: context,
                    title: 'الإدارة المالية',
                    subtitle: 'لوحة تحكم العمليات المالية',
                    icon: Icons.dashboard,
                    onTap: () => context.go('/admin/financial-dashboard'),
                  ),

                  // إدارة المحافظ
                  _buildSettingTile(
                    context: context,
                    title: 'إدارة المحافظ',
                    subtitle: 'إدارة أرصدة المحافظ والمعاملات',
                    icon: Icons.account_balance_wallet,
                    onTap: () => context.go('/admin/wallet-management'),
                  ),
                ],
              ),
            ],
          ),
        );
  }

  /// بناء عنصر الإعدادات
  Widget _buildSettingTile({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Theme.of(context).colorScheme.primary.withAlpha((0.1 * 255).toInt()),
        child: Icon(icon, color: Theme.of(context).colorScheme.primary),
      ),
      title: Text(
        title,
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(fontSize: 14, color: Colors.grey),
      ),
      trailing: const Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Colors.grey,
      ),
      onTap: onTap,
    );
  }
}

/// Widget إحصائيات المشرف السريعة
class AdminQuickStatsWidget extends ConsumerWidget {
  const AdminQuickStatsWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAdmin = ref.watch(isAdminProvider);

    if (!isAdmin) {
      return const SizedBox.shrink();
    }

    return Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.primary.withAlpha((0.8 * 255).toInt()),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Row(
                children: [
                  Icon(Icons.dashboard, color: Colors.white, size: 24),
                  SizedBox(width: 8),
                  Text(
                    'لوحة تحكم المشرف',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              _buildQuickStats(ref),
            ],
          ),
        );
  }

  /// بناء إحصائيات سريعة
  Widget _buildQuickStats(WidgetRef ref) {
    final adminUsersAsync = ref.watch(adminUsersProvider);
    final regularUsersAsync = ref.watch(regularUsersProvider);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildStatCard(
          title: 'المشرفين',
          value: adminUsersAsync.when(
            data: (users) => users.length.toString(),
            loading: () => '-',
            error: (error, stackTrace) => '0',
          ),
          icon: Icons.admin_panel_settings,
        ),
        _buildStatCard(
          title: 'المستخدمين',
          value: regularUsersAsync.when(
            data: (users) => users.length.toString(),
            loading: () => '-',
            error: (error, stackTrace) => '0',
          ),
          icon: Icons.people,
        ),
        _buildStatCard(
          title: 'المجموع',
          value:
              ((adminUsersAsync.asData?.value.length ?? 0) +
                      (regularUsersAsync.asData?.value.length ?? 0))
                  .toString(),
          icon: Icons.group,
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withAlpha((0.2 * 255).toInt()),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.white, size: 20),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          title,
          style: const TextStyle(fontSize: 12, color: Colors.white70),
        ),
      ],
    );
  }
}
