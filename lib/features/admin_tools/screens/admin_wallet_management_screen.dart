import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../core/widgets/app_error_widget.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../models/admin_financial_models.dart';
import '../providers/admin_wallet_providers.dart';
import '../providers/admin_financial_providers.dart';
import '../widgets/admin_dashboard_card.dart';
import '../widgets/wallet_details_card.dart';
import '../widgets/wallet_operations_dialog.dart';
import '../widgets/wallet_search_bar.dart';
import '../widgets/wallet_filters_dialog.dart';

/// شاشة إدارة المحافظ الشاملة
class AdminWalletManagementScreen extends HookConsumerWidget {
  const AdminWalletManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final walletsAsync = ref.watch(adminWalletsNotifierProvider);
    
    final tabController = useTabController(initialLength: 3);
    final searchController = useTextEditingController();
    final scrollController = useScrollController();

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text(
          'إدارة المحافظ',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'المحافظ', icon: Icon(Icons.account_balance_wallet)),
            Tab(text: 'التنبيهات', icon: Icon(Icons.warning)),
            Tab(text: 'النشاطات', icon: Icon(Icons.history)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFiltersDialog(context, ref),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.invalidate(adminWalletsNotifierProvider),
          ),
        ],
      ),
      body: TabBarView(
        controller: tabController,
        children: [
          _buildWalletsTab(context, ref, walletsAsync, searchController, scrollController),
          _buildAlertsTab(context, ref),
          _buildActivitiesTab(context, ref),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showWalletOperationsDialog(context, ref),
        backgroundColor: Theme.of(context).colorScheme.primary,
        icon: const Icon(Icons.add),
        label: const Text('عملية جديدة'),
      ),
    );
  }

  /// بناء تبويب المحافظ
  Widget _buildWalletsTab(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<List<WalletOverview>> walletsAsync,
    TextEditingController searchController,
    ScrollController scrollController,
  ) {
    return Column(
      children: [
        // شريط البحث
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                                          color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: WalletSearchBar(
            controller: searchController,
            onChanged: (query) {
              ref.read(searchStateNotifierProvider.notifier).updateSearch(query);
            },
            onClear: () {
              searchController.clear();
              ref.read(searchStateNotifierProvider.notifier).clearSearch();
            },
          ),
        ),
        
        // المحتوى الرئيسي
        Expanded(
          child: walletsAsync.when(
            data: (wallets) => _buildWalletsContent(
              context,
              ref,
              wallets,
              scrollController,
            ),
            loading: () => const LoadingWidget(),
            error: (error, stack) => AppErrorWidget(
              message: 'فشل في تحميل المحافظ',
              details: error.toString(),
              onRetry: () => ref.invalidate(adminWalletsNotifierProvider),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء محتوى المحافظ
  Widget _buildWalletsContent(
    BuildContext context,
    WidgetRef ref,
    List<WalletOverview> wallets,
    ScrollController scrollController,
  ) {
    if (wallets.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_balance_wallet_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد محافظ',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(adminWalletsNotifierProvider.notifier).refresh();
      },
      child: SingleChildScrollView(
        controller: scrollController,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // إحصائيات سريعة
            _buildQuickStats(context, wallets),
            
            const SizedBox(height: 20),
            
            // قائمة المحافظ
            _buildWalletsList(context, ref, wallets),
          ],
        ),
      ),
    );
  }

  /// بناء الإحصائيات السريعة
  Widget _buildQuickStats(BuildContext context, List<WalletOverview> wallets) {
    final totalWallets = wallets.length;
    final activeWallets = wallets.where((w) => w.isActive).length;
    final lockedWallets = wallets.where((w) => w.isLocked).length;
    final flaggedWallets = wallets.where((w) => w.isFlagged).length;
    final totalBalance = wallets.fold<double>(
      0,
      (sum, wallet) => sum + double.parse(wallet.balance),
    );

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إحصائيات سريعة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            LayoutBuilder(
              builder: (context, constraints) {
                final crossAxisCount = constraints.maxWidth > 800
                    ? 4
                    : constraints.maxWidth > 500
                        ? 2
                        : 1;
                        
                return GridView.count(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  crossAxisCount: crossAxisCount,
                  mainAxisSpacing: 12,
                  crossAxisSpacing: 12,
                  childAspectRatio: 1.5,
                  children: [
                    AdminDashboardCard(
                      title: 'إجمالي المحافظ',
                      value: '$totalWallets',
                      icon: Icons.account_balance_wallet,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    AdminDashboardCard(
                      title: 'المحافظ النشطة',
                      value: '$activeWallets',
                      icon: Icons.check_circle,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    AdminDashboardCard(
                      title: 'المحافظ المجمدة',
                      value: '$lockedWallets',
                      icon: Icons.lock,
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                    AdminDashboardCard(
                      title: 'المحافظ المعلمة',
                      value: '$flaggedWallets',
                      icon: Icons.flag,
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'إجمالي الأرصدة:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${NumberFormat('#,##0.00').format(totalBalance)} دينار',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة المحافظ
  Widget _buildWalletsList(
    BuildContext context,
    WidgetRef ref,
    List<WalletOverview> wallets,
  ) {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: wallets.length,
      itemBuilder: (context, index) {
        final wallet = wallets[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: WalletDetailsCard(
            wallet: wallet,
            onTap: () => _showWalletDetailsDialog(context, ref, wallet),
            onOperations: () => _showWalletOperationsDialog(
              context,
              ref,
              wallet: wallet,
            ),
          ),
        );
      },
    );
  }

  /// بناء تبويب التنبيهات
  Widget _buildAlertsTab(BuildContext context, WidgetRef ref) {
    final alertsAsync = ref.watch(financialAlertsProvider);
    
    return alertsAsync.when(
      data: (alerts) => _buildAlertsContent(context, ref, alerts),
      loading: () => const LoadingWidget(),
      error: (error, stack) => AppErrorWidget(
        message: 'فشل في تحميل التنبيهات',
        details: error.toString(),
        onRetry: () => ref.invalidate(financialAlertsProvider),
      ),
    );
  }

  /// بناء محتوى التنبيهات
  Widget _buildAlertsContent(
    BuildContext context,
    WidgetRef ref,
    List<FinancialAlert> alerts,
  ) {
    if (alerts.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.notifications_off_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد تنبيهات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(financialAlertsProvider);
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: alerts.length,
        itemBuilder: (context, index) {
          final alert = alerts[index];
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: Card(
              elevation: 2,
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: alert.severity.color,
                  child: const Icon(
                    Icons.warning,
                    color: Colors.white,
                  ),
                ),
                title: Text(
                  alert.title,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: Text(alert.description),
                trailing: alert.isResolved
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : IconButton(
                        icon: const Icon(Icons.check),
                        onPressed: () => _resolveAlert(context, ref, alert),
                      ),
                onTap: () => _showAlertDetails(context, ref, alert),
              ),
            ),
          );
        },
      ),
    );
  }

  /// بناء تبويب النشاطات
  Widget _buildActivitiesTab(BuildContext context, WidgetRef ref) {
    final logsAsync = ref.watch(adminActionLogsProvider);
    
    return logsAsync.when(
      data: (logs) => _buildActivitiesContent(context, ref, logs),
      loading: () => const LoadingWidget(),
      error: (error, stack) => AppErrorWidget(
        message: 'فشل في تحميل النشاطات',
        details: error.toString(),
        onRetry: () => ref.invalidate(adminActionLogsProvider),
      ),
    );
  }

  /// بناء محتوى النشاطات
  Widget _buildActivitiesContent(
    BuildContext context,
    WidgetRef ref,
    List<AdminActionLog> logs,
  ) {
    if (logs.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد نشاطات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(adminActionLogsProvider);
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: logs.length,
        itemBuilder: (context, index) {
          final log = logs[index];
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: Card(
              elevation: 2,
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  child: Icon(
                    _getActionIcon(log.actionType),
                    color: Colors.white,
                  ),
                ),
                title: Text(
                  log.actionType.displayName,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(log.description),
                    const SizedBox(height: 4),
                    Text(
                      'بواسطة: ${log.adminEmail} • ${DateFormat('yyyy-MM-dd HH:mm').format(log.createdAt)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
                onTap: () => _showActionDetails(context, ref, log),
              ),
            ),
          );
        },
      ),
    );
  }

  /// الحصول على أيقونة العملية
  IconData _getActionIcon(AdminActionType actionType) {
    switch (actionType) {
      case AdminActionType.walletBalanceAdjustment:
        return Icons.account_balance_wallet;
      case AdminActionType.accountFreeze:
        return Icons.lock;
      case AdminActionType.accountUnfreeze:
        return Icons.lock_open;
      case AdminActionType.transactionReversal:
        return Icons.undo;
      case AdminActionType.manualRefund:
        return Icons.money_off;
      case AdminActionType.suspiciousActivityFlag:
        return Icons.flag;
      default:
        return Icons.admin_panel_settings;
    }
  }

  /// عرض نافذة تفاصيل المحفظة
  void _showWalletDetailsDialog(
    BuildContext context,
    WidgetRef ref,
    WalletOverview wallet,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل المحفظة ${wallet.walletId}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('المستخدم', wallet.userEmail),
              _buildDetailRow('الرصيد', '${wallet.balance} ${wallet.currency}'),
              _buildDetailRow('الحالة', wallet.isActive ? 'نشط' : 'غير نشط'),
              _buildDetailRow('مجمد', wallet.isLocked ? 'نعم' : 'لا'),
              _buildDetailRow('مستوى التحقق', wallet.verificationStatus),
              _buildDetailRow('الحد اليومي', '${wallet.dailyLimit} ${wallet.currency}'),
              _buildDetailRow('الحد الشهري', '${wallet.monthlyLimit} ${wallet.currency}'),
              _buildDetailRow('عدد المعاملات', wallet.totalTransactions.toString()),
              _buildDetailRow('تاريخ الإنشاء', DateFormat('yyyy-MM-dd').format(wallet.createdAt)),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showWalletOperationsDialog(context, ref, wallet: wallet);
            },
            child: const Text('العمليات'),
          ),
        ],
      ),
    );
  }

  /// بناء صف التفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  /// عرض نافذة عمليات المحفظة
  void _showWalletOperationsDialog(
    BuildContext context,
    WidgetRef ref, {
    WalletOverview? wallet,
  }) {
    showDialog(
      context: context,
      builder: (context) => WalletOperationsDialog(
        wallet: wallet,
        onOperation: (operation) {
          Navigator.of(context).pop();
          _handleWalletOperation(context, ref, operation, wallet);
        },
      ),
    );
  }

  /// معالجة عملية المحفظة
  void _handleWalletOperation(
    BuildContext context,
    WidgetRef ref,
    String operation,
    WalletOverview? wallet,
  ) {
    // تنفيذ العمليات المختلفة حسب النوع
    try {
      switch (operation) {
        case 'adjust_balance':
          _showBalanceAdjustmentDialog(context, ref, wallet);
          break;
        case 'freeze':
          _performWalletFreeze(context, ref, wallet);
          break;
        case 'unfreeze':
          _performWalletUnfreeze(context, ref, wallet);
          break;
        case 'update_limits':
          _showLimitsUpdateDialog(context, ref, wallet);
          break;
        default:
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم تنفيذ العملية: $operation'),
              backgroundColor: Theme.of(context).colorScheme.primary,
            ),
          );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تنفيذ العملية: $e'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  /// عرض نافذة تعديل الرصيد
  void _showBalanceAdjustmentDialog(
    BuildContext context,
    WidgetRef ref,
    WalletOverview? wallet,
  ) {
    if (wallet == null) return;
    // يمكن تنفيذ نافذة تعديل الرصيد هنا
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة تعديل الرصيد قيد التطوير')),
    );
  }

  /// تجميد المحفظة
  void _performWalletFreeze(
    BuildContext context,
    WidgetRef ref,
    WalletOverview? wallet,
  ) {
    if (wallet == null) return;
    // يمكن تنفيذ تجميد المحفظة هنا
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة تجميد المحفظة قيد التطوير')),
    );
  }

  /// إلغاء تجميد المحفظة
  void _performWalletUnfreeze(
    BuildContext context,
    WidgetRef ref,
    WalletOverview? wallet,
  ) {
    if (wallet == null) return;
    // يمكن تنفيذ إلغاء تجميد المحفظة هنا
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة إلغاء تجميد المحفظة قيد التطوير')),
    );
  }

  /// عرض نافذة تحديث الحدود
  void _showLimitsUpdateDialog(
    BuildContext context,
    WidgetRef ref,
    WalletOverview? wallet,
  ) {
    if (wallet == null) return;
    // يمكن تنفيذ نافذة تحديث الحدود هنا
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة تحديث الحدود قيد التطوير')),
    );
  }

  /// عرض نافذة الفلاتر
  void _showFiltersDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => WalletFiltersDialog(
        currentFilters: ref.read(walletFiltersNotifierProvider),
        onApplyFilters: (filters) {
          ref.read(walletFiltersNotifierProvider.notifier).updateFilters(filters);
          Navigator.of(context).pop();
        },
      ),
    );
  }

  /// حل التنبيه
  void _resolveAlert(BuildContext context, WidgetRef ref, FinancialAlert alert) {
    // String resolutionNotes = '';
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حل التنبيه'),
        content: const TextField(
          decoration: InputDecoration(
            labelText: 'ملاحظات الحل',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
          // onChanged: (value) {
          //   resolutionNotes = value;
          // },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                // حل التنبيه مع الملاحظات
                // For simple providers, handle resolution via service directly
                // You may want to implement this method in AdminFinancialService  
                // final request = ResolveAlertRequest(
                //   resolutionNotes: resolutionNotes,
                //   resolvedBy: 'current_admin',
                // );
                ref.invalidate(financialAlertsProvider);
                
                if (context.mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('تم حل التنبيه بنجاح'),
                      backgroundColor: Theme.of(context).colorScheme.primary,
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في حل التنبيه: $e'),
                      backgroundColor: Theme.of(context).colorScheme.error,
                    ),
                  );
                }
              }
            },
            child: const Text('حل'),
          ),
        ],
      ),
    );
  }

  /// عرض تفاصيل التنبيه
  void _showAlertDetails(BuildContext context, WidgetRef ref, FinancialAlert alert) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(alert.title),
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('الوصف: ${alert.description}'),
            const SizedBox(height: 8),
            Text('المستوى: ${alert.severity.displayName}'),
            const SizedBox(height: 8),
            Text('التاريخ: ${DateFormat('yyyy-MM-dd HH:mm').format(alert.createdAt)}'),
            if (alert.isResolved) ...[
              const SizedBox(height: 8),
              Text('تم الحل بواسطة: ${alert.resolvedBy ?? 'غير محدد'}'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض تفاصيل النشاط
  void _showActionDetails(BuildContext context, WidgetRef ref, AdminActionLog log) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(log.actionType.displayName),
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('الوصف: ${log.description}'),
            const SizedBox(height: 8),
            Text('السبب: ${log.reason}'),
            const SizedBox(height: 8),
            Text('المدير: ${log.adminEmail}'),
            const SizedBox(height: 8),
            Text('التاريخ: ${DateFormat('yyyy-MM-dd HH:mm').format(log.createdAt)}'),
            if (log.previousValue != null) ...[
              const SizedBox(height: 8),
              Text('القيمة السابقة: ${log.previousValue}'),
            ],
            if (log.newValue != null) ...[
              const SizedBox(height: 8),
              Text('القيمة الجديدة: ${log.newValue}'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
