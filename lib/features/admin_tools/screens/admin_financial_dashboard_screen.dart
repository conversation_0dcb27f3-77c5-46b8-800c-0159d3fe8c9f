import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_color_extensions.dart';
import '../../../core/widgets/app_error_widget.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../models/admin_financial_models.dart';
import '../providers/admin_financial_providers.dart';
import '../widgets/admin_dashboard_card.dart';
import '../widgets/admin_financial_chart.dart';
import '../widgets/admin_alerts_summary.dart';
import '../widgets/admin_recent_actions.dart';
import '../widgets/admin_system_health.dart';

class AdminFinancialDashboardScreen extends ConsumerStatefulWidget {
  const AdminFinancialDashboardScreen({super.key});

  @override
  ConsumerState<AdminFinancialDashboardScreen> createState() =>
      _AdminFinancialDashboardScreenState();
}

class _AdminFinancialDashboardScreenState
    extends ConsumerState<AdminFinancialDashboardScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final dashboardAsync = ref.watch(adminDashboardProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text(
          'لوحة التحكم المالية',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'الملخص'),
            Tab(text: 'التنبيهات'),
            Tab(text: 'الأنشطة'),
            Tab(text: 'الصحة'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.invalidate(adminDashboardProvider);
            },
          ),
        ],
      ),
      body: dashboardAsync.when(
        data: (dashboard) => TabBarView(
          controller: _tabController,
          children: [
            _buildSummaryTab(dashboard),
            _buildAlertsTab(dashboard),
            _buildActivitiesTab(dashboard),
            _buildHealthTab(dashboard),
          ],
        ),
        loading: () => const LoadingWidget(),
        error: (error, stack) => AppErrorWidget(
          message: 'فشل في تحميل لوحة التحكم',
          details: error.toString(),
          onRetry: () => ref.invalidate(adminDashboardProvider),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => context.push('/admin/wallet-management'),
        icon: const Icon(Icons.account_balance_wallet),
        label: const Text('المحافظ'),
      ),
    );
  }

  Widget _buildSummaryTab(AdminDashboard dashboard) {
    return SingleChildScrollView(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الملخص العام
          _buildGeneralSummary(dashboard.financialSummary),

          const SizedBox(height: 20),

          // المعاملات والأنشطة
          _buildTransactionSummary(dashboard.financialSummary),

          const SizedBox(height: 20),

          // الرسوم البيانية
          _buildChartsSection(dashboard.financialSummary),

          const SizedBox(height: 20),

          // الموافقات المعلقة
          _buildPendingApprovals(dashboard.pendingApprovals),
        ],
      ),
    );
  }

  Widget _buildGeneralSummary(FinancialSummary summary) {
    final items = <AdminDashboardCard>[
      AdminDashboardCard(
        title: 'إجمالي الأرصدة',
        value: _formatCurrency(summary.totalPlatformBalance),
        icon: Icons.account_balance_wallet,
        color: context.colors.success,
      ),
      AdminDashboardCard(
        title: 'المحافظ النشطة',
        value: '${summary.activeWallets}/${summary.totalWallets}',
        icon: Icons.wallet,
        color: Theme.of(context).colorScheme.tertiary,
      ),
      AdminDashboardCard(
        title: 'أرباح اليوم',
        value: _formatCurrency(summary.platformEarningsToday),
        icon: Icons.trending_up,
        color: context.colors.success,
      ),
      AdminDashboardCard(
        title: 'أرباح الشهر',
        value: _formatCurrency(summary.platformEarningsMonth),
        icon: Icons.calendar_month,
        color: context.colors.primary,
      ),
    ];

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الملخص العام',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: context.colors.primary,
                  ),
            ),
            const SizedBox(height: 16),
            LayoutBuilder(
              builder: (context, constraints) {
                final crossAxisCount = constraints.maxWidth > 800
                    ? 4
                    : constraints.maxWidth > 500
                        ? 2
                        : 1;
                return GridView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: items.length,
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: crossAxisCount,
                    mainAxisExtent: 140,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  itemBuilder: (context, index) => items[index],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionSummary(FinancialSummary summary) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعاملات والأنشطة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: context.colors.primary,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: AdminDashboardCard(
                    title: 'معاملات اليوم',
                    value: summary.totalTransactionsToday.toString(),
                    icon: Icons.swap_horiz,
                    color: Theme.of(context).colorScheme.tertiary,
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: AdminDashboardCard(
                    title: 'حجم التداول',
                    value: _formatCurrency(summary.totalVolumeToday),
                    icon: Icons.show_chart,
                    color: context.colors.success,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Row(
              children: [
                Expanded(
                  child: AdminDashboardCard(
                    title: 'معاملات معلقة',
                    value: summary.pendingTransactions.toString(),
                    icon: Icons.pending_actions,
                    color: context.colors.warning,
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: AdminDashboardCard(
                    title: 'معاملات فاشلة',
                    value: summary.failedTransactionsToday.toString(),
                    icon: Icons.error,
                    color: context.colors.error,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartsSection(FinancialSummary summary) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الرسوم البيانية',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: context.colors.primary,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: AdminFinancialChart(
                title: 'أداء المنصة',
                data: {
                  'الأرباح': double.parse(summary.platformEarningsToday),
                  'المعاملات': summary.totalTransactionsToday.toDouble(),
                  'المستخدمون الجدد': summary.newUsersToday.toDouble(),
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPendingApprovals(int pendingApprovals) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الموافقات المعلقة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: context.colors.primary,
              ),
            ),
            const SizedBox(height: 16),
            AdminDashboardCard(
              title: 'طلبات الموافقة',
              value: pendingApprovals.toString(),
              icon: Icons.approval,
              color: pendingApprovals > 0
                  ? context.colors.warning
                  : context.colors.success,
              onTap: () {
                // Navigate to approvals screen
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertsTab(AdminDashboard dashboard) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AdminAlertsSummary(
            totalAlerts: dashboard.financialSummary.activeAlerts,
            criticalAlerts: dashboard.financialSummary.criticalAlerts,
            resolvedToday: dashboard.financialSummary.resolvedAlertsToday,
          ),
          const SizedBox(height: 20),
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'التنبيهات الحديثة',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: context.colors.primary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ...dashboard.recentAlerts.map(_buildAlertItem),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlertItem(FinancialAlert alert) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: alert.severity.color,
          child: Icon(
            _getAlertIcon(alert.alertType),
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Text(
          alert.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          alert.description,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: alert.severity.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                alert.severity.displayName,
                style: TextStyle(
                  color: alert.severity.color,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              DateFormat('HH:mm').format(alert.createdAt),
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        onTap: () {
          // Navigate to alert details
        },
      ),
    );
  }

  Widget _buildActivitiesTab(AdminDashboard dashboard) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AdminRecentActions(
            recentActions: dashboard.recentAdminActions,
            onViewAll: () {
              // Navigate to full audit log
            },
          ),
          const SizedBox(height: 20),
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المعاملات الحديثة',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: context.colors.primary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ...dashboard.recentTransactions.map(_buildTransactionItem),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(TransactionAnalysis transaction) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: transaction.isSuspicious
              ? context.colors.error
              : context.colors.success,
          child: Icon(
            transaction.isSuspicious ? Icons.warning : Icons.check_circle,
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Text(
          _formatCurrency(transaction.amount),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text('${transaction.type} - ${transaction.status}'),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'المخاطر: ${transaction.riskScore.toStringAsFixed(1)}',
              style: TextStyle(
                color: transaction.riskScore > 0.7
                    ? context.colors.error
                    : transaction.riskScore > 0.4
                    ? context.colors.warning
                    : context.colors.success,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              DateFormat('HH:mm').format(transaction.createdAt),
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        onTap: () {
          // Navigate to transaction details
        },
      ),
    );
  }

  Widget _buildHealthTab(AdminDashboard dashboard) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [AdminSystemHealth(systemHealth: dashboard.systemHealth)],
      ),
    );
  }

  String _formatCurrency(String amount) {
    try {
      final double value = double.parse(amount);
      final formatter = NumberFormat.currency(
        locale: 'ar_SA',
        symbol: 'LYD',
        decimalDigits: 2,
      );
      return formatter.format(value);
    } catch (e) {
      return amount;
    }
  }

  IconData _getAlertIcon(String alertType) {
    switch (alertType.toLowerCase()) {
      case 'suspicious_transaction':
        return Icons.warning;
      case 'high_volume':
        return Icons.trending_up;
      case 'failed_transaction':
        return Icons.error;
      case 'verification_required':
        return Icons.verified_user;
      default:
        return Icons.notification_important;
    }
  }
}
