import 'package:flutter/material.dart';

import '../models/admin_financial_models.dart';

class WalletOperationsDialog extends StatelessWidget {
  const WalletOperationsDialog({
    super.key,
    this.wallet,
    this.onOperation,
  });

  final WalletOverview? wallet;
  final ValueChanged<String>? onOperation;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        wallet != null 
            ? 'عمليات المحفظة ${wallet!.walletId}'
            : 'العمليات المالية',
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildOperationTile(
              context,
              icon: Icons.account_balance_wallet,
              title: 'تعديل الرصيد',
              subtitle: 'زيادة أو نقصان الرصيد',
              onTap: () => onOperation?.call('adjust_balance'),
            ),
            const SizedBox(height: 8),
            _buildOperationTile(
              context,
              icon: Icons.lock,
              title: wallet?.isLocked == true ? 'إلغاء التجميد' : 'تجميد المحفظة',
              subtitle: wallet?.isLocked == true 
                  ? 'إلغاء تجميد المحفظة' 
                  : 'تجميد المحفظة مؤقتاً',
              onTap: () => onOperation?.call(
                wallet?.isLocked == true ? 'unfreeze' : 'freeze',
              ),
            ),
            const SizedBox(height: 8),
            _buildOperationTile(
              context,
              icon: Icons.settings,
              title: 'تعديل الحدود',
              subtitle: 'تعديل الحدود اليومية والشهرية',
              onTap: () => onOperation?.call('update_limits'),
            ),
            const SizedBox(height: 8),
            _buildOperationTile(
              context,
              icon: Icons.flag,
              title: 'تعليم كمشبوه',
              subtitle: 'إضافة علامة نشاط مشبوه',
              onTap: () => onOperation?.call('flag_suspicious'),
            ),
            const SizedBox(height: 8),
            _buildOperationTile(
              context,
              icon: Icons.history,
              title: 'سجل المعاملات',
              subtitle: 'عرض تاريخ المعاملات',
              onTap: () => onOperation?.call('view_transactions'),
            ),
            const SizedBox(height: 8),
            _buildOperationTile(
              context,
              icon: Icons.warning,
              title: 'إنشاء تنبيه',
              subtitle: 'إنشاء تنبيه مالي جديد',
              onTap: () => onOperation?.call('create_alert'),
            ),
            const SizedBox(height: 8),
            _buildOperationTile(
              context,
              icon: Icons.assessment,
              title: 'تقرير مفصل',
              subtitle: 'إنشاء تقرير عن المحفظة',
              onTap: () => onOperation?.call('generate_report'),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
      ],
    );
  }

  Widget _buildOperationTile(BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: EdgeInsets.zero,
      child: ListTile(
        leading: CircleAvatar(
                                backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
          child: Icon(
            icon,
            color: Theme.of(context).colorScheme.primary,
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }
} 