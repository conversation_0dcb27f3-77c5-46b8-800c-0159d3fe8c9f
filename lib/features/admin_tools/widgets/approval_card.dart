import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../models/admin_financial_models.dart';
import '../providers/admin_approval_providers.dart';
import 'risk_badge.dart';

class ApprovalCard extends HookConsumerWidget {
  const ApprovalCard({super.key, required this.tx});

  final TransactionAnalysis tx;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isProcessing = useState(false);

    Future<void> handleAction(bool approve) async {
      if (isProcessing.value) return;
      isProcessing.value = true;
      try {
        await ref.read(
          approveTransactionProvider(
            ApprovalParams(transactionId: tx.transactionId, approve: approve),
          ).future,
        );
      } catch (e) {
        if (!context.mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل العملية: $e')),
        );
      } finally {
        isProcessing.value = false;
      }
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text('#${tx.transactionId.substring(0, 8)}',
                    style: Theme.of(context).textTheme.titleSmall),
                const SizedBox(width: 8),
                RiskBadge(level: tx.riskScore > 0.7
                    ? 'high'
                    : tx.riskScore >= 0.4
                        ? 'medium'
                        : 'low'),
                const Spacer(),
                Text('${tx.amount} LYD',
                    style: Theme.of(context).textTheme.titleMedium),
              ],
            ),
            const SizedBox(height: 8),
            Text('نوع المعاملة: ${tx.type}'),
            Text('الحالة: ${tx.status}'),
            const SizedBox(height: 8),
            if (isProcessing.value)
              const LinearProgressIndicator()
            else
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => handleAction(false),
                      icon: const Icon(Icons.close, color: Colors.red),
                      label: const Text('رفض'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => handleAction(true),
                      icon: const Icon(Icons.check),
                      label: const Text('موافقة'),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }
} 