import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/theme/carnow_colors.dart';
import '../models/cart_model.dart';
import '../models/cart_item_model.dart';

/// Enhanced Cart Item Card following Forever Plan architecture
/// 
/// Features:
/// - Material 3 design system
/// - Optimized images with caching
/// - Quantity controls with validation
/// - Accessibility support
/// - RTL support for Arabic
/// - Smooth animations
class EnhancedCartItemCard extends StatefulWidget {
  const EnhancedCartItemCard({
    super.key,
    required this.item,
    required this.onQuantityChanged,
    required this.onRemove,
  });

  final CartItemModel item;
  final Function(int quantity) onQuantityChanged;
  final VoidCallback onRemove;

  @override
  State<EnhancedCartItemCard> createState() => _EnhancedCartItemCardState();
}

class _EnhancedCartItemCardState extends State<EnhancedCartItemCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isUpdating = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Card(
            elevation: 2,
            clipBehavior: Clip.antiAlias,
            child: InkWell(
              onTap: () => _animatePress(),
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildProductImage(),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildProductDetails(),
                    ),
                    _buildQuantityControls(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildProductImage() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[100],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: widget.item.productImage != null
            ? CachedNetworkImage(
                imageUrl: widget.item.productImage!,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey[200],
                  child: const Center(
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey[200],
                  child: const Icon(
                    Icons.image_not_supported_outlined,
                    color: Colors.grey,
                    size: 32,
                  ),
                ),
              )
            : Container(
                color: Colors.grey[200],
                child: const Icon(
                  Icons.shopping_bag_outlined,
                  color: Colors.grey,
                  size: 32,
                ),
              ),
      ),
    );
  }

  Widget _buildProductDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.item.productName,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Text(
          widget.item.formattedPrice,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: CarnowColors.primary,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Text(
              'المجموع: ',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Text(
              widget.item.formattedItemTotal,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                color: CarnowColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuantityControls() {
    return Column(
      children: [
        // Remove button
        IconButton(
          onPressed: _isUpdating ? null : _showRemoveDialog,
          icon: const Icon(Icons.delete_outline),
          iconSize: 20,
          tooltip: 'إزالة من العربة',
          style: IconButton.styleFrom(
            foregroundColor: Theme.of(context).colorScheme.error,
          ),
        ),
        
        // Quantity controls
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildQuantityButton(
                icon: Icons.remove,
                onPressed: widget.item.quantity > 1
                    ? () => _updateQuantity(widget.item.quantity - 1)
                    : null,
              ),
              Container(
                width: 40,
                height: 32,
                alignment: Alignment.center,
                child: _isUpdating
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : Text(
                        '${widget.item.quantity}',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
              _buildQuantityButton(
                icon: Icons.add,
                onPressed: widget.item.quantity < 99
                    ? () => _updateQuantity(widget.item.quantity + 1)
                    : null,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildQuantityButton({
    required IconData icon,
    required VoidCallback? onPressed,
  }) {
    return SizedBox(
      width: 32,
      height: 32,
      child: IconButton(
        onPressed: _isUpdating ? null : onPressed,
        icon: Icon(icon, size: 16),
        padding: EdgeInsets.zero,
        style: IconButton.styleFrom(
          foregroundColor: CarnowColors.primary,
        ),
      ),
    );
  }

  void _animatePress() {
    _animationController.forward().then((_) {
      _animationController.reverse();
    });
  }

  Future<void> _updateQuantity(int newQuantity) async {
    if (_isUpdating || newQuantity < 1 || newQuantity > 99) return;

    setState(() {
      _isUpdating = true;
    });

    try {
      await widget.onQuantityChanged(newQuantity);
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  Future<void> _showRemoveDialog() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إزالة من العربة'),
        content: Text('هل تريد إزالة "${widget.item.productName}" من العربة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('إزالة'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isUpdating = true;
      });

      try {
        widget.onRemove();
      } finally {
        if (mounted) {
          setState(() {
            _isUpdating = false;
          });
        }
      }
    }
  }
}
