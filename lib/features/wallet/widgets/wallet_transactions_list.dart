import 'package:flutter/material.dart';
import '../models/wallet_transaction_model.dart';
import '../../../core/utils/date_formatter.dart';

/// قائمة المعاملات المالية
class WalletTransactionsList extends StatelessWidget {
  const WalletTransactionsList({
    super.key,
    required this.transactions,
    this.onLoadMore,
  });
  final List<WalletTransactionModel> transactions;
  final VoidCallback? onLoadMore;

  @override
  Widget build(BuildContext context) {
    if (transactions.isEmpty) {
      return const _EmptyTransactionsList();
    }

    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: transactions.length + (onLoadMore != null ? 1 : 0),
      separatorBuilder: (context, index) => const SizedBox(height: 8),
      itemBuilder: (context, index) {
        if (index == transactions.length) {
          return _LoadMoreButton(onTap: onLoadMore!);
        }

        final transaction = transactions[index];
        return _TransactionTile(transaction: transaction);
      },
    );
  }
}

/// بلاطة معاملة واحدة
class _TransactionTile extends StatelessWidget {
  const _TransactionTile({required this.transaction});
  final WalletTransactionModel transaction;

  @override
  Widget build(BuildContext context) {
    final isPositive = transaction.isCredit;
    final color = isPositive ? Colors.green : Colors.red;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1)),
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              _getTransactionIcon(transaction.type),
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.typeMessage,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
                if (transaction.description?.isNotEmpty == true) ...[
                  const SizedBox(height: 4),
                  Text(
                    transaction.description!,
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  ),
                ],
                const SizedBox(height: 4),
                Text(
                  DateFormatter.formatDateTime(transaction.createdAt),
                  style: TextStyle(color: Colors.grey[500], fontSize: 12),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${isPositive ? '+' : ''}${transaction.amount.toStringAsFixed(2)}',
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(
                    transaction.status,
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  transaction.statusMessage,
                  style: TextStyle(
                    color: _getStatusColor(transaction.status),
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getTransactionIcon(WalletTransactionType type) {
    switch (type) {
      case WalletTransactionType.deposit:
        return Icons.add_circle_outline;
      case WalletTransactionType.withdraw:
        return Icons.remove_circle_outline;
      case WalletTransactionType.transferIn:
        return Icons.call_received;
      case WalletTransactionType.transferOut:
        return Icons.call_made;
      case WalletTransactionType.fee:
        return Icons.monetization_on_outlined;
      case WalletTransactionType.refund:
        return Icons.refresh;
    }
  }

  Color _getStatusColor(WalletTransactionStatus status) {
    switch (status) {
      case WalletTransactionStatus.pending:
        return Colors.orange;
      case WalletTransactionStatus.completed:
        return Colors.green;
      case WalletTransactionStatus.failed:
        return Colors.red;
      case WalletTransactionStatus.cancelled:
        return Colors.grey;
    }
  }
}

/// قائمة فارغة
class _EmptyTransactionsList extends StatelessWidget {
  const _EmptyTransactionsList();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.receipt_long_outlined, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'لا توجد معاملات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر معاملاتك هنا عند إجرائها',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// زر تحميل المزيد
class _LoadMoreButton extends StatelessWidget {
  const _LoadMoreButton({required this.onTap});
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: colorScheme.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: colorScheme.primary.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.expand_more, color: colorScheme.primary),
            const SizedBox(width: 8),
            Text(
              'تحميل المزيد',
              style: TextStyle(
                color: colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
