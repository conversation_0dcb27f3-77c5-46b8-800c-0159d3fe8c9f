import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
// No hooks used currently; if needed, re-add flutter_hooks.
import '../../taxonomy/providers/taxonomy_providers.dart';

/// BrowseScreen
/// Allows buyers to navigate the taxonomy hierarchy
/// Sections (NavigationRail) -> Categories grid -> SubCategories grid
class BrowseScreen extends HookConsumerWidget {
  const BrowseScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final taxonomyAsync = ref.watch(allSectionsProvider);
    final selected = ref.watch(selectedTaxonomyProvider);

    return Scaffold(
      appBar: AppBar(title: const Text('Browse Products')),
      body: taxonomyAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (err, st) => Center(child: Text('Error: $err')),
        data: (sections) {
          final selectedSection =
              selected.$1 ?? (sections.isNotEmpty ? sections.first : null);
          final sectionIndex = selectedSection == null
              ? 0
              : sections.indexWhere((s) => s.id == selectedSection.id);

          final categories = selectedSection?.categories ?? [];
          final selectedCategory = selected.$2;
          final subCategories = selectedCategory?.subCategories ?? [];

          return Row(
            children: [
              NavigationRail(
                selectedIndex: sectionIndex,
                onDestinationSelected: (idx) {
                  ref
                      .read(selectedTaxonomyProvider.notifier)
                      .selectSection(sections[idx]);
                },
                labelType: NavigationRailLabelType.all,
                destinations: [
                  for (final section in sections)
                    NavigationRailDestination(
                      icon: const Icon(Icons.category_outlined),
                      selectedIcon: const Icon(Icons.category),
                      label: Text(section.nameEn),
                    ),
                ],
              ),
              const VerticalDivider(width: 1),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Categories',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: GridView.count(
                          crossAxisCount: 2,
                          childAspectRatio: 3,
                          children: [
                            for (final cat in categories)
                              GestureDetector(
                                onTap: () => ref
                                    .read(selectedTaxonomyProvider.notifier)
                                    .selectCategory(cat),
                                child: Card(
                                  color: selectedCategory?.id == cat.id
                                      ? Theme.of(
                                          context,
                                        ).colorScheme.primaryContainer
                                      : null,
                                  child: Center(child: Text(cat.nameEn)),
                                ),
                              ),
                          ],
                        ),
                      ),
                      if (selectedCategory != null) ...[
                        const SizedBox(height: 16),
                        Text(
                          'Sub Categories',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 8),
                        Expanded(
                          child: GridView.count(
                            crossAxisCount: 2,
                            childAspectRatio: 3,
                            children: [
                              for (final sub in subCategories)
                                GestureDetector(
                                  onTap: () {
                                    // Navigate to products listing with filter
                                    context.pushNamed(
                                      'subcategory-products',
                                      pathParameters: {'id': sub.id},
                                      extra: sub,
                                    );
                                  },
                                  child: Card(
                                    child: Center(child: Text(sub.nameEn)),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
