import 'package:flutter/material.dart';

import '../models/subscription_model.dart';
import '../models/seller_enums.dart';

/// Subscription Plan Card Widget - Material 3 Design
/// Forever Plan Architecture: Flutter (UI Only)
/// 
/// ✅ Material 3 design system components
/// ✅ Accessibility features following WCAG guidelines
/// ✅ Bilingual support (Arabic/English)
/// ✅ Interactive selection states
/// ✅ UI only - no business logic
class SubscriptionPlanCard extends StatelessWidget {
  final SubscriptionPlan plan;
  final bool isSelected;
  final VoidCallback onSelected;

  const SubscriptionPlanCard({
    super.key,
    required this.plan,
    required this.isSelected,
    required this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isSelected ? 8.0 : 2.0,
      shadowColor: isSelected 
          ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
          : Theme.of(context).shadowColor,
      child: InkWell(
        onTap: onSelected,
        borderRadius: BorderRadius.circular(12.0),
        child: Container(
          padding: const EdgeInsets.all(20.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.0),
            border: isSelected
                ? Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2.0,
                  )
                : null,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with plan name and selection indicator
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          plan.nameAr,
                          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4.0),
                        Text(
                          plan.name,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Selection indicator
                  Container(
                    width: 24.0,
                    height: 24.0,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.outline,
                        width: 2.0,
                      ),
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : Colors.transparent,
                    ),
                    child: isSelected
                        ? Icon(
                            Icons.check,
                            size: 16.0,
                            color: Theme.of(context).colorScheme.onPrimary,
                          )
                        : null,
                  ),
                ],
              ),
              
              const SizedBox(height: 16.0),
              
              // Plan description
              Text(
                plan.descriptionAr,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  height: 1.4,
                ) ?? const TextStyle(),
              ),
              
              const SizedBox(height: 16.0),
              
              // Pricing information
              _buildPricingSection(context),
              
              const SizedBox(height: 16.0),
              
              // Features list
              if (plan.features?.isNotEmpty == true) ...[
                Text(
                  'المميزات:',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ) ?? const TextStyle(),
                ),
                const SizedBox(height: 8.0),
                
                ...(plan.benefitsAr ?? []).take(3).map((benefit) => Padding(
                  padding: const EdgeInsets.only(bottom: 4.0),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle_rounded,
                        color: Theme.of(context).colorScheme.primary,
                        size: 16.0,
                      ),
                      const SizedBox(width: 8.0),
                      Expanded(
                        child: Text(
                          benefit,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ) ?? const TextStyle(),
                        ),
                      ),
                    ],
                  ),
                )),
                
                if ((plan.benefitsAr?.length ?? 0) > 3) ...[
                  const SizedBox(height: 4.0),
                  Text(
                    'و ${(plan.benefitsAr?.length ?? 0) - 3} مميزات أخرى...',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontStyle: FontStyle.italic,
                    ) ?? const TextStyle(),
                  ),
                ],
              ],
              
              // Popular badge for recommended plans
              if (plan.isPopular) ...[
                const SizedBox(height: 12.0),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12.0,
                    vertical: 6.0,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(16.0),
                  ),
                  child: Text(
                    'الأكثر شيوعاً',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.w600,
                    ) ?? const TextStyle(),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPricingSection(BuildContext context) {
    if (plan.tier == SubscriptionTier.starter) {
      return Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 16.0,
          vertical: 12.0,
        ),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.secondaryContainer,
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.star_rounded,
              color: Theme.of(context).colorScheme.onSecondaryContainer,
              size: 20.0,
            ),
            const SizedBox(width: 8.0),
            Text(
              'مجاني',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSecondaryContainer,
                fontWeight: FontWeight.w600,
              ) ?? const TextStyle(),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Monthly pricing
        Row(
          children: [
            Text(
              '${plan.monthlyPriceLD}',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w700,
              ),
            ),
            const SizedBox(width: 4.0),
            Text(
              'د.ل / شهر',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ) ?? const TextStyle(),
            ),
          ],
        ),
        
        const SizedBox(height: 4.0),
        
        // Yearly pricing with discount
        if (plan.yearlyPriceLD < plan.monthlyPriceLD * 12) ...[
          Row(
            children: [
              Text(
                '${plan.yearlyPriceLD}',
                style: Theme.of(context).textTheme.labelLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(width: 4.0),
              Text(
                'د.ل / سنة',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ) ?? const TextStyle(),
              ),
              const SizedBox(width: 8.0),
              
              // Discount badge
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8.0,
                  vertical: 2.0,
                ),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.errorContainer,
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: Text(
                  'وفر ${_calculateYearlyDiscount()}%',
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Theme.of(context).colorScheme.onErrorContainer,
                    fontWeight: FontWeight.w600,
                  ) ?? const TextStyle(),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  int _calculateYearlyDiscount() {
    if (plan.tier == SubscriptionTier.starter) return 0;
    
    final monthlyTotal = plan.monthlyPriceLD * 12;
    final yearlyPrice = plan.yearlyPriceLD;
    final discount = ((monthlyTotal - yearlyPrice) / monthlyTotal * 100).round();
    
    return discount;
  }
}
