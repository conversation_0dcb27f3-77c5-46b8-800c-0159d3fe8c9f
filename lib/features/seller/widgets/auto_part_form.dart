import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/models/enums.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../providers/vehicle_data_provider.dart';

class AutoPartForm extends StatefulWidget {
  const AutoPartForm({super.key});

  @override
  State<AutoPartForm> createState() => _AutoPartFormState();
}

class _AutoPartFormState extends State<AutoPartForm> {
  final _brandController = TextEditingController();
  final _partNumberController = TextEditingController();
  final _manufacturerController = TextEditingController();
  final _yearFromController = TextEditingController();
  final _yearToController = TextEditingController();
  final _amperageController = TextEditingController();
  final _warrantyController = TextEditingController();
  final _dimensionsController = TextEditingController();

  String? _selectedCategory;
  BatteryType? _selectedBatteryType;
  BatteryVoltage? _selectedBatteryVoltage;
  final List<String> _compatibleVehicles = [];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'تفاصيل قطعة الغيار',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),

          // Brand Field
          TextFormField(
            controller: _brandController,
            decoration: const InputDecoration(
              labelText: 'الماركة / العلامة التجارية',
              hintText: 'مثال: Toyota, BMW, Bosch',
              border: InputBorder.none,
              prefixIcon: Icon(Icons.business),
            ),
            textCapitalization: TextCapitalization.words,
            validator: (value) {
              if (value?.isEmpty ?? true) {
                return 'الماركة مطلوبة';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Part Number
          TextFormField(
            controller: _partNumberController,
            decoration: const InputDecoration(
              labelText: 'رقم القطعة',
              hintText: 'مثال: 12345-ABC-123',
              border: InputBorder.none,
              prefixIcon: Icon(Icons.tag),
            ),
            textCapitalization: TextCapitalization.characters,
            validator: (value) {
              if (value?.isEmpty ?? true) {
                return 'رقم القطعة مطلوب';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Manufacturer (Optional)
          TextFormField(
            controller: _manufacturerController,
            decoration: const InputDecoration(
              labelText: 'الشركة المصنعة (اختياري)',
              hintText: 'مثال: Denso, Valeo, NGK',
              border: InputBorder.none,
              prefixIcon: Icon(Icons.factory),
            ),
            textCapitalization: TextCapitalization.words,
          ),
          const SizedBox(height: 16),

          // Year Range
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _yearFromController,
                  decoration: const InputDecoration(
                    labelText: 'من سنة',
                    hintText: '2010',
                    border: InputBorder.none,
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(4),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _yearToController,
                  decoration: const InputDecoration(
                    labelText: 'إلى سنة',
                    hintText: '2020',
                    border: InputBorder.none,
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(4),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Compatible Vehicles
          _buildCompatibleVehiclesSection(),
          const SizedBox(height: 16),

          // Battery-specific fields (show only if battery category is selected)
          if (_selectedCategory == 'batteries') ...[
            const Divider(),
            const SizedBox(height: 16),
            Text(
              'مواصفات البطارية',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),

            // Battery Type
            DropdownButtonFormField<BatteryType>(
              value: _selectedBatteryType,
              decoration: const InputDecoration(
                labelText: 'نوع البطارية',
                border: InputBorder.none,
                prefixIcon: Icon(Icons.battery_std),
              ),
              items: BatteryType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(_getBatteryTypeName(type)),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedBatteryType = value;
                });
              },
              validator: (value) {
                if (_selectedCategory == 'batteries' && value == null) {
                  return 'نوع البطارية مطلوب';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Battery Voltage
            DropdownButtonFormField<BatteryVoltage>(
              value: _selectedBatteryVoltage,
              decoration: const InputDecoration(
                labelText: 'الفولتية',
                border: InputBorder.none,
                prefixIcon: Icon(Icons.electrical_services),
              ),
              items: BatteryVoltage.values.map((voltage) {
                return DropdownMenuItem(
                  value: voltage,
                  child: Text(_getBatteryVoltageName(voltage)),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedBatteryVoltage = value;
                });
              },
              validator: (value) {
                if (_selectedCategory == 'batteries' && value == null) {
                  return 'الفولتية مطلوبة';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Amperage
            TextFormField(
              controller: _amperageController,
              decoration: const InputDecoration(
                labelText: 'قوة التيار (أمبير)',
                hintText: 'مثال: 60',
                border: InputBorder.none,
                prefixIcon: Icon(Icons.flash_on),
                suffixText: 'Ah',
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              validator: (value) {
                if (_selectedCategory == 'batteries' &&
                    (value?.isEmpty ?? true)) {
                  return 'قوة التيار مطلوبة';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Warranty
            TextFormField(
              controller: _warrantyController,
              decoration: const InputDecoration(
                labelText: 'فترة الضمان (اختياري)',
                hintText: 'مثال: سنة واحدة',
                border: InputBorder.none,
                prefixIcon: Icon(Icons.verified),
              ),
            ),
            const SizedBox(height: 16),

            // Dimensions
            TextFormField(
              controller: _dimensionsController,
              decoration: const InputDecoration(
                labelText: 'الأبعاد (اختياري)',
                hintText: 'مثال: 24.2 x 17.5 x 19 سم',
                border: InputBorder.none,
                prefixIcon: Icon(Icons.straighten),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCompatibleVehiclesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'السيارات المتوافقة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: _addCompatibleVehicle,
              icon: const Icon(Icons.add),
              label: const Text('إضافة سيارة'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (_compatibleVehicles.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.3),
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Text(
              'لم يتم إضافة سيارات متوافقة بعد.\nاضغط "إضافة سيارة" لتحديد السيارات المتوافقة.',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
          )
        else
          ...(_compatibleVehicles.asMap().entries.map((entry) {
            final index = entry.key;
            final vehicle = entry.value;
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: const Icon(Icons.directions_car),
                title: Text(vehicle),
                trailing: IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: () => _removeCompatibleVehicle(index),
                ),
              ),
            );
          }).toList()),
      ],
    );
  }

  void _addCompatibleVehicle() {
    showDialog(
      context: context,
      builder: (context) => _VehicleSelectionDialog(
        onVehicleSelected: (vehicle) {
          setState(() {
            if (!_compatibleVehicles.contains(vehicle)) {
              _compatibleVehicles.add(vehicle);
            }
          });
        },
      ),
    );
  }

  void _removeCompatibleVehicle(int index) {
    setState(() {
      _compatibleVehicles.removeAt(index);
    });
  }

  String _getBatteryTypeName(BatteryType type) {
    switch (type) {
      case BatteryType.leadAcid:
        return 'حمض الرصاص (Lead Acid)';
      case BatteryType.agm:
        return 'AGM (خالية من الصيانة)';
      case BatteryType.gel:
        return 'جل (Gel)';
      case BatteryType.lithium:
        return 'ليثيوم (Lithium)';
      case BatteryType.maintenanceFree:
        return 'خالية من الصيانة';
      case BatteryType.other:
        return 'أخرى';
    }
  }

  String _getBatteryVoltageName(BatteryVoltage voltage) {
    switch (voltage) {
      case BatteryVoltage.twelveVolt:
        return '12 فولت';
      case BatteryVoltage.twentyFourVolt:
        return '24 فولت';
      case BatteryVoltage.sixVolt:
        return '6 فولت';
      case BatteryVoltage.other:
        return 'أخرى';
    }
  }

  @override
  void dispose() {
    _brandController.dispose();
    _partNumberController.dispose();
    _manufacturerController.dispose();
    _yearFromController.dispose();
    _yearToController.dispose();
    _amperageController.dispose();
    _warrantyController.dispose();
    _dimensionsController.dispose();
    super.dispose();
  }
}

class _VehicleSelectionDialog extends HookConsumerWidget {
  const _VehicleSelectionDialog({required this.onVehicleSelected});
  final Function(String) onVehicleSelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedMakeId = useState<int?>(null);
    final selectedModel = useState<String?>(null);
    final selectedYear = useState<String?>(null);

    // Get dynamic vehicle data
    final makesAsyncValue = ref.watch(sellerVehicleMakesProvider);
    final modelsAsyncValue = selectedMakeId.value != null
        ? ref.watch(sellerVehicleModelsProvider(makeId: selectedMakeId.value!))
        : const AsyncValue.data(<dynamic>[]);

    return AlertDialog(
      title: const Text('إضافة سيارة متوافقة'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Make Selection - Dynamic
            makesAsyncValue.when(
              loading: () => const CircularProgressIndicator(),
              error: (error, _) => Text('خطأ: $error'),
              data: (makes) => DropdownButtonFormField<int>(
                value: selectedMakeId.value,
                decoration: const InputDecoration(
                  labelText: 'الماركة',
                  border: InputBorder.none,
                ),
                items: makes.map((make) {
                  return DropdownMenuItem(
                    value: make.id,
                    child: Text(make.name),
                  );
                }).toList(),
                onChanged: (value) {
                  selectedMakeId.value = value;
                  selectedModel.value = null; // Reset model when make changes
                },
              ),
            ),
            const SizedBox(height: 16),

            // Model Selection - Dynamic
            modelsAsyncValue.when(
              loading: () => const CircularProgressIndicator(),
              error: (error, _) => Text('خطأ: $error'),
              data: (models) {
                final modelItems = models.map((model) {
                  return DropdownMenuItem<String>(
                    value: model.name,
                    child: Text(model.name),
                  );
                }).toList();

                return DropdownButtonFormField<String>(
                  value: selectedModel.value,
                  decoration: const InputDecoration(
                    labelText: 'الموديل',
                    hintText: 'اختر الموديل',
                    border: InputBorder.none,
                  ),
                  items: modelItems,
                  onChanged: (value) {
                    selectedModel.value = value;
                  },
                );
              },
            ),
            const SizedBox(height: 16),

            // Year Input
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'السنة (اختياري)',
                hintText: 'مثال: 2015',
                border: InputBorder.none,
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(4),
              ],
              onChanged: (value) {
                selectedYear.value = value;
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        FilledButton(
          onPressed: _canAddVehicle(selectedMakeId.value, selectedModel.value)
              ? () => _addVehicle(
                  context,
                  makesAsyncValue.value,
                  selectedMakeId.value,
                  selectedModel.value,
                  selectedYear.value,
                )
              : null,
          child: const Text('إضافة'),
        ),
      ],
    );
  }

  bool _canAddVehicle(int? makeId, String? model) {
    return makeId != null && model != null && model.isNotEmpty;
  }

  void _addVehicle(
    BuildContext context,
    List<dynamic>? makes,
    int? makeId,
    String? model,
    String? year,
  ) {
    if (makes == null || makeId == null || model == null) return;

    final make = makes.firstWhere((m) => m.id == makeId);
    String vehicle = '${make.name} $model';
    if (year != null && year.isNotEmpty) {
      vehicle += ' ($year)';
    }

    onVehicleSelected(vehicle);
    Navigator.of(context).pop();
  }
}
