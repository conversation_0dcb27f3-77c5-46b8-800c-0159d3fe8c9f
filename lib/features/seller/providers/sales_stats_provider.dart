import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/networking/simple_api_client.dart';

part 'sales_stats_provider.g.dart';

/// Sales Analytics Model - Forever Plan Compliant
/// 
/// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data + Auth)
/// - Real data only from Supabase database
/// - No mock data in production
/// - All statistics calculated from actual database records
class SalesAnalyticsModel {
  final int totalSales;
  final double totalRevenue;
  final int totalProducts;
  final int totalCustomers;
  final List<DailySales> dailySales;
  final List<ProductSaleStats> topProducts;
  final Map<String, dynamic> metadata;

  const SalesAnalyticsModel({
    required this.totalSales,
    required this.totalRevenue,
    required this.totalProducts,
    required this.totalCustomers,
    required this.dailySales,
    required this.topProducts,
    required this.metadata,
  });

  factory SalesAnalyticsModel.fromJson(Map<String, dynamic> json) {
    return SalesAnalyticsModel(
      totalSales: json['total_sales'] ?? 0,
      totalRevenue: (json['total_revenue'] ?? 0.0).toDouble(),
      totalProducts: json['total_products'] ?? 0,
      totalCustomers: json['total_customers'] ?? 0,
      dailySales: (json['daily_sales'] as List<dynamic>?)
          ?.map((e) => DailySales.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      topProducts: (json['top_products'] as List<dynamic>?)
          ?.map((e) => ProductSaleStats.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      metadata: json['metadata'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_sales': totalSales,
      'total_revenue': totalRevenue,
      'total_products': totalProducts,
      'total_customers': totalCustomers,
      'daily_sales': dailySales.map((e) => e.toJson()).toList(),
      'top_products': topProducts.map((e) => e.toJson()).toList(),
      'metadata': metadata,
    };
  }
}

/// Daily Sales Model - Forever Plan Compliant
class DailySales {
  final String date;
  final int sales;
  final double revenue;

  const DailySales({
    required this.date,
    required this.sales,
    required this.revenue,
  });

  factory DailySales.fromJson(Map<String, dynamic> json) {
    return DailySales(
      date: json['date'] ?? '',
      sales: json['sales'] ?? 0,
      revenue: (json['revenue'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date,
      'sales': sales,
      'revenue': revenue,
    };
  }
}

/// Product Sale Stats Model - Forever Plan Compliant
class ProductSaleStats {
  final String productId;
  final String productName;
  final int salesCount;
  final double revenue;
  final String category;

  const ProductSaleStats({
    required this.productId,
    required this.productName,
    required this.salesCount,
    required this.revenue,
    required this.category,
  });

  factory ProductSaleStats.fromJson(Map<String, dynamic> json) {
    return ProductSaleStats(
      productId: json['product_id'] ?? '',
      productName: json['product_name'] ?? '',
      salesCount: json['sales_count'] ?? 0,
      revenue: (json['revenue'] ?? 0.0).toDouble(),
      category: json['category'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'product_id': productId,
      'product_name': productName,
      'sales_count': salesCount,
      'revenue': revenue,
      'category': category,
    };
  }
}

/// Sales Stats Provider - Forever Plan Compliant
/// 
/// Forever Plan: Real data only from Supabase database
/// NO MOCK DATA - All statistics from actual database records
@riverpod
class SalesStats extends _$SalesStats {
  @override
  Future<SalesAnalyticsModel> build() async {
    // Forever Plan: Real data only from Supabase via Go API
    return await _fetchSalesAnalytics();
  }

  /// Fetch real sales analytics from Supabase database
  /// Forever Plan: No mock data - real database queries only
  Future<SalesAnalyticsModel> _fetchSalesAnalytics() async {
    try {
      final apiClient = ref.read(simpleApiClientProvider);
      
      // Forever Plan: Real data from Supabase database
      final response = await apiClient.get<Map<String, dynamic>>(
        '/api/v1/seller/analytics/sales',
      );

      if (response.isSuccess && response.data != null) {
        return SalesAnalyticsModel.fromJson(response.data!);
      } else {
        throw Exception('Failed to fetch sales analytics: ${response.error}');
      }
    } catch (e) {
      // Forever Plan: Proper error handling without mock data fallback
      throw Exception('Failed to fetch sales analytics: $e');
    }
  }

  /// Refresh sales analytics
  /// Forever Plan: Real data refresh from Supabase
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _fetchSalesAnalytics());
  }
}

/// Daily Sales Chart Data Provider - Forever Plan Compliant
/// 
/// Forever Plan: Real data only from Supabase database
@riverpod
Future<List<DailySales>> dailySalesChartData(Ref ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    // Forever Plan: Real data from Supabase database
    final response = await apiClient.get<List<dynamic>>(
      '/api/v1/seller/analytics/daily-sales',
    );

    if (response.isSuccess && response.data != null) {
      return response.data!
          .map((e) => DailySales.fromJson(e as Map<String, dynamic>))
          .toList();
    } else {
      throw Exception('Failed to fetch daily sales: ${response.error}');
    }
  } catch (e) {
    // Forever Plan: Proper error handling without mock data fallback
    throw Exception('Failed to fetch daily sales: $e');
  }
}

/// Top Selling Products Provider - Forever Plan Compliant
/// 
/// Forever Plan: Real data only from Supabase database
@riverpod
Future<List<ProductSaleStats>> topSellingProducts(Ref ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    // Forever Plan: Real data from Supabase database
    final response = await apiClient.get<List<dynamic>>(
      '/api/v1/seller/analytics/top-products',
    );

    if (response.isSuccess && response.data != null) {
      return response.data!
          .map((e) => ProductSaleStats.fromJson(e as Map<String, dynamic>))
          .toList();
    } else {
      throw Exception('Failed to fetch top products: ${response.error}');
    }
  } catch (e) {
    // Forever Plan: Proper error handling without mock data fallback
    throw Exception('Failed to fetch top products: $e');
  }
}
