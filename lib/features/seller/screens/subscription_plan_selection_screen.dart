import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/subscription_model.dart';
import '../models/seller_enums.dart';

class SubscriptionPlanSelectionScreen extends ConsumerStatefulWidget {
  const SubscriptionPlanSelectionScreen({super.key});

  @override
  ConsumerState<SubscriptionPlanSelectionScreen> createState() =>
      _SubscriptionPlanSelectionScreenState();
}

class _SubscriptionPlanSelectionScreenState
    extends ConsumerState<SubscriptionPlanSelectionScreen> {
  late ValueNotifier<BillingCycle> selectedCycle;
  late ValueNotifier<SubscriptionPlan?> selectedPlan;

  @override
  void initState() {
    super.initState();
    selectedCycle = ValueNotifier(BillingCycle.monthly);
    selectedPlan = ValueNotifier<SubscriptionPlan?>(null);
  }

  @override
  void dispose() {
    selectedCycle.dispose();
    selectedPlan.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'اختيار خطة الاشتراك',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رسالة ترحيبية
            _buildWelcomeMessage(),
            const SizedBox(height: 32),

            // اختيار نوع الفترة
            _buildBillingCycleSelection(),
            const SizedBox(height: 32),

            // عنوان الخطط
            const Text(
              'اختر الخطة المناسبة لك',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'يمكنك تغيير خطتك في أي وقت لاحقاً',
              style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
            ),
            const SizedBox(height: 24),

            // قائمة الخطط
            _buildPlansList(),
            const SizedBox(height: 32),

            // زر المتابعة
            _buildContinueButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeMessage() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.store, color: Colors.white, size: 24),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'ابدأ رحلتك كبائع معنا! 🚀',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'اختر الخطة التي تناسب نشاطك التجاري واستمتع بمميزات البيع على منصتنا. نحن هنا لدعمك في كل خطوة!',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade700,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBillingCycleSelection() {
    return ValueListenableBuilder<BillingCycle>(
      valueListenable: selectedCycle,
      builder: (context, cycle, _) {
        return Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Expanded(
                child: _buildCycleOption(
                  'شهري',
                  BillingCycle.monthly,
                  cycle == BillingCycle.monthly,
                  null,
                ),
              ),
              Expanded(
                child: _buildCycleOption(
                  'سنوي',
                  BillingCycle.yearly,
                  cycle == BillingCycle.yearly,
                  'وفر 15%',
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCycleOption(
    String title,
    BillingCycle cycle,
    bool isSelected,
    String? savings,
  ) {
    return GestureDetector(
      onTap: () => selectedCycle.value = cycle,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Column(
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isSelected ? Theme.of(context).colorScheme.primary : Colors.grey.shade600,
              ),
            ),
            if (savings != null) ...[
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  savings,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPlansList() {
    // خطط أساسية محددة مسبقاً مع تخفيض 15% للاشتراك السنوي
    final plans = [
      const SubscriptionPlan(
        id: 'starter',
        name: 'Starter',
        nameAr: 'المبتدئ',
        description: 'Perfect for new sellers',
        descriptionAr: 'مثالي للبائعين الجدد',
        tier: SubscriptionTier.starter,
        monthlyPriceLD: 75,
        yearlyPriceLD: 765, // 75 * 12 * 0.85 = 765 (تخفيض 15%)
        monthlyListingQuota: 50,
        additionalListingFeeLD: 1,
      ),
      const SubscriptionPlan(
        id: 'basic',
        name: 'Basic',
        nameAr: 'أساسي',
        description: 'For active sellers',
        descriptionAr: 'للبائعين النشطين',
        tier: SubscriptionTier.basic,
        monthlyPriceLD: 200,
        yearlyPriceLD: 2040, // 200 * 12 * 0.85 = 2040 (تخفيض 15%)
        monthlyListingQuota: 250,
        additionalListingFeeLD: 0.75,
        isPopular: true,
      ),
      const SubscriptionPlan(
        id: 'premium',
        name: 'Premium',
        nameAr: 'مميز',
        description: 'For professional sellers',
        descriptionAr: 'للبائعين المحترفين',
        tier: SubscriptionTier.premium,
        monthlyPriceLD: 500,
        yearlyPriceLD: 5100, // 500 * 12 * 0.85 = 5100 (تخفيض 15%)
        monthlyListingQuota: 1000,
        additionalListingFeeLD: 0.5,
      ),
    ];

    return ValueListenableBuilder<BillingCycle>(
      valueListenable: selectedCycle,
      builder: (context, cycle, _) {
        return Column(
          children: plans.map((plan) => _buildPlanCard(plan, cycle)).toList(),
        );
      },
    );
  }

  Widget _buildPlanCard(SubscriptionPlan plan, BillingCycle cycle) {
    return ValueListenableBuilder<SubscriptionPlan?>(
      valueListenable: selectedPlan,
      builder: (context, selected, _) {
        final isSelected = selected?.id == plan.id;
        final price = cycle == BillingCycle.yearly
            ? plan.yearlyPriceLD
            : plan.monthlyPriceLD;
        final monthlyEquivalent = cycle == BillingCycle.yearly
            ? plan.yearlyPriceLD / 12
            : plan.monthlyPriceLD;
        final savings = cycle == BillingCycle.yearly
            ? (plan.monthlyPriceLD * 12) - plan.yearlyPriceLD
            : 0;

        return GestureDetector(
          onTap: () => selectedPlan.value = plan,
          child: Container(
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected ? Theme.of(context).colorScheme.primary : Colors.grey.shade300,
                width: isSelected ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Stack(
              children: [
                // شارة الأكثر شيوعاً
                if (plan.isPopular)
                  Positioned(
                    top: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(16),
                          bottomLeft: Radius.circular(16),
                        ),
                      ),
                      child: const Text(
                        'الأكثر شيوعاً',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),

                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          // دائرة التحديد
                          Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: isSelected
                                    ? Theme.of(context).colorScheme.primary
                                    : Colors.grey.shade400,
                                width: 2,
                              ),
                              color: isSelected
                                  ? Theme.of(context).colorScheme.primary
                                  : Colors.transparent,
                            ),
                            child: isSelected
                                ? const Icon(
                                    Icons.check,
                                    size: 14,
                                    color: Colors.white,
                                  )
                                : null,
                          ),
                          const SizedBox(width: 12),

                          // اسم الخطة
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  plan.nameAr,
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                                Text(
                                  plan.descriptionAr,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // السعر
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              if (cycle == BillingCycle.yearly &&
                                  savings > 0) ...[
                                Text(
                                  '${plan.monthlyPriceLD * 12} LD',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey.shade500,
                                    decoration: TextDecoration.lineThrough,
                                  ),
                                ),
                                const SizedBox(height: 2),
                              ],
                              Text(
                                '${price.toInt()} LD',
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),
                              Text(
                                cycle == BillingCycle.yearly
                                    ? '${monthlyEquivalent.toStringAsFixed(0)} LD/شهر'
                                    : '/شهر',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // المميزات
                      _buildFeatureItem(
                        '${plan.monthlyListingQuota} إعلان شهرياً',
                      ),
                      _buildFeatureItem(
                        'رسوم إعلان إضافي: ${plan.additionalListingFeeLD} LD',
                      ),
                      _buildFeatureItem('دعم فني 24/7'),
                      _buildFeatureItem('إحصائيات مفصلة'),

                      // شارة التوفير للاشتراك السنوي
                      if (cycle == BillingCycle.yearly && savings > 0) ...[
                        const SizedBox(height: 12),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.green.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.green.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.savings,
                                size: 16,
                                color: Colors.green,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                'توفر ${savings.toInt()} LD سنوياً',
                                style: const TextStyle(
                                  fontSize: 13,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(Icons.check_circle, size: 16, color: Colors.green.shade600),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContinueButton() {
    return ValueListenableBuilder<SubscriptionPlan?>(
      valueListenable: selectedPlan,
      builder: (context, plan, _) {
        final isEnabled = plan != null;

        return SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton(
            onPressed: isEnabled ? _onContinue : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              disabledBackgroundColor: Colors.grey.shade300,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: isEnabled ? 2 : 0,
            ),
            child: Text(
              isEnabled ? 'متابعة إلى بيانات المتجر' : 'اختر خطة للمتابعة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isEnabled ? Colors.white : Colors.grey.shade600,
              ),
            ),
          ),
        );
      },
    );
  }

  void _onContinue() {
    if (selectedPlan.value != null) {
      context.push(
        '/seller/registration-details',
        extra: {
          'selectedPlan': selectedPlan.value!,
          'selectedCycle': selectedCycle.value,
        },
      );
    }
  }
}
