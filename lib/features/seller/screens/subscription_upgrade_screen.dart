import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_color_extensions.dart';
import '../../../core/widgets/app_error_widget.dart';
import '../models/subscription_model.dart';
import '../models/seller_enums.dart';

import '../providers/subscription_provider.dart';

class SubscriptionUpgradeScreen extends HookConsumerWidget {
  const SubscriptionUpgradeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final subscriptionAsync = ref.watch(sellerSubscriptionProviderProvider);
    final plansAsync = ref.watch(availableSubscriptionPlansProvider);
    final selectedPlan = useState<SubscriptionPlan?>(null);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'ترقية الاشتراك',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        foregroundColor: Theme.of(context).textTheme.bodyLarge?.color,
      ),
      body: subscriptionAsync.when(
        data: (subscription) => plansAsync.when(
          data: (plans) =>
              _buildContent(context, ref, subscription, plans, selectedPlan),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stackTrace) => AppErrorWidget(
            message: error.toString(),
            onRetry: () => ref.refresh(availableSubscriptionPlansProvider),
          ),
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => AppErrorWidget(
          message: error.toString(),
          onRetry: () => ref.refresh(sellerSubscriptionProviderProvider),
        ),
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    WidgetRef ref,
    SellerSubscription? currentSubscription,
    List<SubscriptionPlan> plans,
    ValueNotifier<SubscriptionPlan?> selectedPlan,
  ) {
    final currentPlan = currentSubscription != null
        ? PredefinedPlans.getPlanById(currentSubscription.planId)
        : null;

    return Column(
      children: [
        // Current plan header
        if (currentPlan != null)
          _buildCurrentPlanHeader(context, currentPlan, currentSubscription!),

        // Available plans
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: plans.length,
            itemBuilder: (context, index) {
              final plan = plans[index];
              final isCurrent = currentPlan?.id == plan.id;
              final isUpgrade =
                  currentPlan != null && _isPlanUpgrade(currentPlan, plan);
              final isDowngrade =
                  currentPlan != null && _isPlanDowngrade(currentPlan, plan);

              return _buildPlanCard(
                context,
                plan,
                selectedPlan,
                isCurrent: isCurrent,
                isUpgrade: isUpgrade,
                isDowngrade: isDowngrade,
              );
            },
          ),
        ),

        // Upgrade button
        if (selectedPlan.value != null &&
            selectedPlan.value!.id != currentPlan?.id)
          _buildUpgradeButton(
            context,
            ref,
            selectedPlan.value!,
            currentPlan,
            currentSubscription,
          ),
      ],
    );
  }

  Widget _buildCurrentPlanHeader(
    BuildContext context,
    SubscriptionPlan currentPlan,
    SellerSubscription subscription,
  ) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'خطتك الحالية',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      currentPlan.nameAr,
                      style: Theme.of(context).textTheme.headlineSmall
                          ?.copyWith(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${subscription.priceLD.toInt()} د.ل',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  Text(
                    subscription.billingCycle == BillingCycle.yearly
                        ? 'سنوياً'
                        : 'شهرياً',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildCurrentPlanStat(
                context,
                'الإعلانات المتاحة',
                '${subscription.monthlyListingQuota}',
                Icons.campaign,
              ),
              const SizedBox(width: 16),
              _buildCurrentPlanStat(
                context,
                'رسوم إضافية',
                '${subscription.additionalListingFeeLD} د.ل',
                Icons.add_circle_outline,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentPlanStat(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(icon, color: Theme.of(context).colorScheme.primary, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    value,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    label,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlanCard(
    BuildContext context,
    SubscriptionPlan plan,
    ValueNotifier<SubscriptionPlan?> selectedPlan, {
    required bool isCurrent,
    required bool isUpgrade,
    required bool isDowngrade,
  }) {
    final isSelected = selectedPlan.value?.id == plan.id;

    Color getBorderColor() {
      if (isCurrent) return Theme.of(context).colorScheme.primary;
      if (isSelected) return Theme.of(context).colorScheme.secondary;
      return Colors.grey.shade300;
    }

    Color getBackgroundColor() {
      if (isCurrent) return Theme.of(context).colorScheme.primary.withValues(alpha: 0.05);
      if (isSelected) return Theme.of(context).colorScheme.secondary.withValues(alpha: 0.05);
      return Colors.white;
    }

    return GestureDetector(
      onTap: isCurrent ? null : () => selectedPlan.value = plan,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: getBackgroundColor(),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: getBorderColor(),
            width: isCurrent || isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),

              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Plan header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            plan.nameAr,
                            style: Theme.of(context).textTheme.titleLarge
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                          if (plan.isPopular) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.orange,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                'الأكثر شعبية',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                          if (isCurrent) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.primary,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                'الخطة الحالية',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        plan.descriptionAr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${plan.monthlyPriceLD.toInt()} د.ل',
                      style: Theme.of(context).textTheme.headlineSmall
                          ?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: isCurrent ? Theme.of(context).colorScheme.primary : null,
                          ),
                    ),
                    Text(
                      'شهرياً',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                    if (isUpgrade)
                      Container(
                        margin: const EdgeInsets.only(top: 4),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: context.colors.success.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'ترقية',
                          style: TextStyle(
                            color: context.colors.success,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    if (isDowngrade)
                      Container(
                        margin: const EdgeInsets.only(top: 4),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.orange.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          'تخفيض',
                          style: TextStyle(
                            color: Colors.orange,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Plan features comparison
            Row(
              children: [
                Expanded(
                  child: _buildFeatureComparison(
                    context,
                    'الإعلانات الشهرية',
                    '${plan.monthlyListingQuota}',
                    Icons.campaign,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildFeatureComparison(
                    context,
                    'رسوم إضافية',
                    '${plan.additionalListingFeeLD} د.ل',
                    Icons.attach_money,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Additional features
            if (plan.benefitsAr != null && plan.benefitsAr!.isNotEmpty) ...[
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: plan.benefitsAr!.take(3).map((benefit) {
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      benefit,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey.shade700,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureComparison(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey.shade600),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: Theme.of(
                    context,
                  ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
                ),
                Text(
                  label,
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade600),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpgradeButton(
    BuildContext context,
    WidgetRef ref,
    SubscriptionPlan selectedPlan,
    SubscriptionPlan? currentPlan,
    SellerSubscription? currentSubscription,
  ) {
    final isUpgrade =
        currentPlan != null && _isPlanUpgrade(currentPlan, selectedPlan);
    final isDowngrade =
        currentPlan != null && _isPlanDowngrade(currentPlan, selectedPlan);

    String getButtonText() {
      if (isUpgrade) return 'ترقية إلى ${selectedPlan.nameAr}';
      if (isDowngrade) return 'التغيير إلى ${selectedPlan.nameAr}';
      return 'اختيار خطة ${selectedPlan.nameAr}';
    }

    Color getButtonColor() {
      if (isUpgrade) return context.colors.success;
      if (isDowngrade) return Colors.orange;
      return Theme.of(context).colorScheme.primary;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isDowngrade) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.orange, size: 20),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'التغيير إلى خطة أقل سيطبق في بداية الدورة القادمة',
                        style: TextStyle(color: Colors.orange, fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
            ],
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => _handleUpgrade(
                  context,
                  ref,
                  selectedPlan,
                  currentSubscription,
                  isUpgrade,
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: getButtonColor(),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  getButtonText(),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool _isPlanUpgrade(SubscriptionPlan current, SubscriptionPlan target) {
    // Simple comparison based on monthly price
    return target.monthlyPriceLD > current.monthlyPriceLD;
  }

  bool _isPlanDowngrade(SubscriptionPlan current, SubscriptionPlan target) {
    // Simple comparison based on monthly price
    return target.monthlyPriceLD < current.monthlyPriceLD;
  }

  Future<void> _handleUpgrade(
    BuildContext context,
    WidgetRef ref,
    SubscriptionPlan newPlan,
    SellerSubscription? currentSubscription,
    bool isUpgrade,
  ) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isUpgrade ? 'تأكيد الترقية' : 'تأكيد التغيير'),
        content: Text(
          isUpgrade
              ? 'هل تريد ترقية اشتراكك إلى خطة ${newPlan.nameAr}؟ ستدفع الفرق فوراً.'
              : 'هل تريد التغيير إلى خطة ${newPlan.nameAr}؟ سيطبق التغيير في بداية الدورة القادمة.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(isUpgrade ? 'ترقية' : 'تغيير'),
          ),
        ],
      ),
    );

    if (confirmed == true && context.mounted) {
      try {
        await ref
            .read(sellerSubscriptionProviderProvider.notifier)
            .upgradePlan(newPlan: newPlan, immediate: isUpgrade);

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                isUpgrade
                    ? 'تم ترقية اشتراكك بنجاح!'
                    : 'تم جدولة تغيير خطة الاشتراك',
              ),
              backgroundColor: Theme.of(context).colorScheme.primary,
            ),
          );
          context.pop();
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
