/// شاشة قائمة منتجات البائع
///
/// تعرض قائمة بجميع المنتجات التي أضافها البائع إلى متجره.
/// تتيح للبائع استعراض منتجاته، البحث فيها، وتوفر اختصارات
/// لإجراء تعديلات سريعة أو حذف منتج.
library;

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';

import '../../../l10n/app_localizations.dart';
import '../../products/models/product_model.dart';
import '../../../shared/widgets/primary_button.dart';
import '../providers/seller_products_provider.dart';
import '../../../core/widgets/app_error_widget.dart';

final _logger = Logger('SellerProductsManagementScreen');

class SellerProductsManagementScreen extends HookConsumerWidget {
  const SellerProductsManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final productsAsync = ref.watch(sellerProductsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المنتجات'),
        actions: [
          // فلتر للمنتجات
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'تصفية المنتجات',
            onPressed: () => _showFilterOptions(context, ref),
          ),
          // زر إضافة منتج جديد
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => context.push('/seller/add-product'),
            tooltip: 'إضافة منتج جديد',
          ),
        ],
      ),
      // متع المستخدم بالانتعاش عند السحب لأسفل
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(sellerProductsProvider);
        },
        child: productsAsync.when(
          data: (products) {
            if (products.isEmpty) {
              return _buildEmptyState(context, theme, l10n);
            }

            return _buildProductsList(context, theme, products, ref);
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) {
            _logger.severe('Error loading products: $error');
            return AppErrorWidget(
              message: l10n.errorLoadingProducts,
              details: error.toString(),
              onRetry: () => ref.invalidate(sellerProductsProvider),
            );
          },
        ),
      ),
      // زر عائم لإضافة منتج جديد
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.push('/seller/add-product'),
        tooltip: l10n.addProduct,
        child: const Icon(Icons.add),
      ),
    );
  }

  // عرض حالة فارغة عندما لا توجد منتجات
  Widget _buildEmptyState(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
  ) => Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.inventory_2_outlined,
          size: 64,
          color: theme.colorScheme.primary.withAlpha((0.5 * 255).round()),
        ),
        const SizedBox(height: 16),
        Text(l10n.noProductsYet, style: theme.textTheme.titleMedium),
        const SizedBox(height: 24),
        PrimaryButton(
          onPressed: () => context.push('/seller/add-product'),
          text: l10n.addProduct,
        ),
      ],
    ),
  );

  // بناء قائمة المنتجات
  Widget _buildProductsList(
    BuildContext context,
    ThemeData theme,
    List<ProductModel> products,
    WidgetRef ref,
  ) {
    // تنسيق العملة
    final currencyFormat = NumberFormat.currency(
      symbol: 'د.ل',
      decimalDigits: 3,
    );

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: products.length,
      itemBuilder: (context, index) {
        final product = products[index];
        return _ProductListItem(
          product: product,
          currencyFormat: currencyFormat,
          onTap: () => context.push('/seller/products/${product.id}'),
          onEdit: () => context.push('/seller/products/${product.id}/edit'),
          onDelete: () => _confirmDelete(context, ref, product.id),
        );
      },
    );
  }

  // عرض خيارات الفلترة
  void _showFilterOptions(BuildContext context, WidgetRef ref) {
    showModalBottomSheet<void>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (_) => Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'تصفية المنتجات',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.check_circle_outline),
              title: const Text('المنتجات النشطة'),
              onTap: () {
                // تطبيق فلتر المنتجات النشطة
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.access_time),
              title: const Text('قيد المراجعة'),
              onTap: () {
                // تطبيق فلتر المنتجات قيد المراجعة
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.timelapse),
              title: const Text('الحجز المبكر'),
              onTap: () {
                // تطبيق فلتر منتجات الحجز المبكر
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.not_interested),
              title: const Text('المنتجات غير النشطة'),
              onTap: () {
                // تطبيق فلتر المنتجات غير النشطة
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.sell),
              title: const Text('المنتجات المباعة'),
              onTap: () {
                // تطبيق فلتر المنتجات المباعة
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  // تأكيد حذف المنتج
  Future<void> _confirmDelete(
    BuildContext context,
    WidgetRef ref,
    String? productId,
  ) async {
    if (productId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cannot delete product with null ID'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المنتج'),
        content: const Text(
          'هل أنت متأكد من رغبتك في حذف هذا المنتج؟ '
          'لا يمكن التراجع عن هذا الإجراء.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (result == true && context.mounted) {
      _logger.info('Deleting product: $productId');
      try {
        await ref
            .read(sellerProductsProvider.notifier)
            .deleteProduct(productId);
        if (context.mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('تم حذف المنتج بنجاح')));
        }
      } catch (e) {
        _logger.severe('Error deleting product: $e');
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل حذف المنتج: ${e.toString()}'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }
  }
}

/// A widget that displays a single product in the seller's list.
class _ProductListItem extends StatelessWidget {
  const _ProductListItem({
    required this.product,
    required this.currencyFormat,
    this.onTap,
    this.onEdit,
    this.onDelete,
  });

  final ProductModel product;
  final NumberFormat currencyFormat;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    // Determine the product status and color
    final statusText = (product.isAvailable == true)
        ? l10n.active
        : l10n.inactive;
    final statusColor = (product.isAvailable == true)
        ? Colors.green.shade700
        : Colors.orange.shade700;

    // Format price
    final priceText = currencyFormat.format(product.price);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: theme.dividerColor.withAlpha((0.5 * 255).toInt()),
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product Image
                  _buildProductImage(context),
                  const SizedBox(width: 12),
                  // Product Details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          product.name,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          priceText,
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(Icons.circle, size: 12, color: statusColor),
                            const SizedBox(width: 6),
                            Text(
                              statusText,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: statusColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // Actions Menu
                  _buildActionsMenu(context),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                product.description ?? 'No description available',
                style: theme.textTheme.bodyMedium,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProductImage(BuildContext context) {
    final imageUrl = product.images.isNotEmpty ? product.images.first : null;

    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: SizedBox(
        width: 80,
        height: 80,
        child: imageUrl != null
            ? Image.network(
                imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Image.asset(
                  'assets/images/placeholder.png',
                  fit: BoxFit.cover,
                ),
              )
            : Image.asset('assets/images/placeholder.png', fit: BoxFit.cover),
      ),
    );
  }

  Widget _buildActionsMenu(BuildContext context) {
    return PopupMenuButton<String>(
      onSelected: (value) {
        if (value == 'edit' && onEdit != null) {
          onEdit!();
        } else if (value == 'delete' && onDelete != null) {
          onDelete!();
        }
      },
      itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
        const PopupMenuItem<String>(
          value: 'edit',
          child: ListTile(leading: Icon(Icons.edit), title: Text('Edit')),
        ),
        const PopupMenuItem<String>(
          value: 'delete',
          child: ListTile(leading: Icon(Icons.delete), title: Text('Delete')),
        ),
      ],
    );
  }
}
