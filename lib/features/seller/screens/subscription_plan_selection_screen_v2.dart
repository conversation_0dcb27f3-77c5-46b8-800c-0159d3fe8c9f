import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:carnow/core/theme/text_styles.dart';
import 'package:carnow/features/seller/models/discount_model.dart';
import 'package:carnow/features/seller/models/seller_enums.dart';
import 'package:carnow/features/seller/models/subscription_model.dart';
import 'package:carnow/features/seller/providers/subscription_providers.dart';

/// شاشة اختيار خطة الاشتراك المحدثة - تستخدم البيانات من قاعدة البيانات
class SubscriptionPlanSelectionScreenV2 extends ConsumerStatefulWidget {
  const SubscriptionPlanSelectionScreenV2({super.key});

  @override
  ConsumerState<SubscriptionPlanSelectionScreenV2> createState() =>
      _SubscriptionPlanSelectionScreenV2State();
}

class _SubscriptionPlanSelectionScreenV2State
    extends ConsumerState<SubscriptionPlanSelectionScreenV2> {
  BillingCycle selectedCycle = BillingCycle.monthly;
  String? selectedDiscountCode;
  final TextEditingController _discountCodeController = TextEditingController();

  @override
  void dispose() {
    _discountCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final plansAsync = ref.watch(subscriptionPlansWithDiscountsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('اختر خطة الاشتراك'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: plansAsync.when(
        data: (plans) => _buildContent(plans),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text('حدث خطأ في تحميل الخطط', style: CarNowTextStyles.sectionTitle),
              const SizedBox(height: 8),
              Text(error.toString(), style: CarNowTextStyles.productDescription),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.refresh(subscriptionPlansWithDiscountsProvider),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContent(List<SubscriptionPlanWithDiscounts> plans) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان الشاشة
          Text(
            'اختر الخطة المناسبة لك',
            style: CarNowTextStyles.pageTitle.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جميع الخطط تشمل دعم فني وإحصائيات مفصلة',
            style: CarNowTextStyles.productDescription.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),

          // اختيار دورة الفوترة
          _buildBillingCycleSelector(),
          const SizedBox(height: 24),

          // رمز الخصم
          _buildDiscountCodeSection(),
          const SizedBox(height: 24),

          // قائمة الخطط
          _buildPlansList(plans),
          const SizedBox(height: 24),

          // زر المتابعة
          _buildContinueButton(plans),
        ],
      ),
    );
  }

  Widget _buildBillingCycleSelector() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'دورة الفوترة',
              style: CarNowTextStyles.categoryTitle.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildBillingCycleOption(
                    BillingCycle.monthly,
                    'شهري',
                    'مرونة أكبر',
                    Icons.calendar_month,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildBillingCycleOption(
                    BillingCycle.yearly,
                    'سنوي',
                    'توفير ${_getYearlyDiscountPercentage(selectedCycle)}%',
                    Icons.calendar_today,
                    isPopular: true,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBillingCycleOption(
    BillingCycle cycle,
    String title,
    String subtitle,
    IconData icon, {
    bool isPopular = false,
  }) {
    final isSelected = selectedCycle == cycle;
    
    return GestureDetector(
      onTap: () => setState(() => selectedCycle = cycle),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1) : Colors.grey[50],
          border: Border.all(
            color: isSelected ? Theme.of(context).colorScheme.primary : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            if (isPopular)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.tertiary,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'الأكثر توفيراً',
                  style: CarNowTextStyles.discountPercentage.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            const SizedBox(height: 8),
            Icon(
              icon,
              color: isSelected ? Theme.of(context).colorScheme.primary : Colors.grey[600],
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: CarNowTextStyles.productName.copyWith(
                fontWeight: FontWeight.bold,
                color: isSelected ? Theme.of(context).colorScheme.primary : Colors.black87,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: CarNowTextStyles.technicalInfo.copyWith(
                color: isSelected ? Theme.of(context).colorScheme.primary : Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiscountCodeSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'رمز الخصم (اختياري)',
              style: CarNowTextStyles.fieldLabel.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _discountCodeController,
                    decoration: InputDecoration(
                      hintText: 'أدخل رمز الخصم',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      suffixIcon: const Icon(Icons.discount),
                    ),
                    onChanged: (value) {
                      setState(() {
                        selectedDiscountCode = value.isNotEmpty ? value : null;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: selectedDiscountCode != null
                      ? () => _validateDiscountCode(selectedDiscountCode!)
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.tertiary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('تحقق'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlansList(List<SubscriptionPlanWithDiscounts> plans) {
    return Column(
      children: plans.map((planWithDiscount) {
        final plan = planWithDiscount.plan;
        return _buildPlanCard(plan, planWithDiscount);
      }).toList(),
    );
  }

  Widget _buildPlanCard(
    SubscriptionPlan plan,
    SubscriptionPlanWithDiscounts planWithDiscount,
  ) {
    final isPopular = plan.isPopular;
    final currentPrice = selectedCycle == BillingCycle.monthly
        ? plan.monthlyPriceLD
        : planWithDiscount.yearlyPriceWithDiscount;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: isPopular ? 4 : 2,
      child: Container(
        decoration: isPopular
            ? BoxDecoration(
                border: Border.all(color: Theme.of(context).colorScheme.tertiary, width: 2),
                borderRadius: BorderRadius.circular(12),
              )
            : null,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              plan.nameAr,
                              style: CarNowTextStyles.categoryTitle.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                            if (isPopular) ...[
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).colorScheme.tertiary,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'الأكثر شعبية',
                                  style: CarNowTextStyles.technicalInfo.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          plan.descriptionAr,
                          style: CarNowTextStyles.productDescription.copyWith(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${currentPrice.toStringAsFixed(0)} د.ل',
                        style: CarNowTextStyles.priceMain.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      Text(
                        selectedCycle == BillingCycle.monthly ? '/شهر' : '/سنة',
                        style: CarNowTextStyles.technicalInfo.copyWith(color: Colors.grey[600]),
                      ),
                      if (selectedCycle == BillingCycle.yearly) ...[
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.green.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'توفير ${planWithDiscount.yearlyDiscountPercentage.toStringAsFixed(0)}%',
                            style: CarNowTextStyles.technicalInfo.copyWith(
                              color: Colors.green,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Features
              Text(
                'المميزات:',
                style: CarNowTextStyles.fieldLabel.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ...plan.features?.entries.map((entry) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.check_circle,
                          color: Colors.green,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            '${entry.key}: ${entry.value}',
                            style: CarNowTextStyles.productDescription,
                          ),
                        ),
                      ],
                    ),
                  )) ??
                  [],
              const SizedBox(height: 16),

              // Action Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => _selectPlan(plan),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isPopular ? Theme.of(context).colorScheme.tertiary : Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'اختيار هذه الخطة',
                    style: CarNowTextStyles.buttonPrimary.copyWith(color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContinueButton(List<SubscriptionPlanWithDiscounts> plans) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => _showPlanComparison(plans),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.grey[200],
          foregroundColor: Colors.black87,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          'مقارنة جميع الخطط',
          style: CarNowTextStyles.buttonPrimary,
        ),
      ),
    );
  }

  double _getYearlyDiscountPercentage(BillingCycle cycle) {
    if (cycle == BillingCycle.yearly) {
      // يمكن الحصول على هذه القيمة من API
      return 15.0; // قيمة افتراضية
    }
    return 0.0;
  }

  void _validateDiscountCode(String code) async {
    try {
      final discountCode = await ref.read(
        validateDiscountCodeProvider(code).future,
      );
      
      if (discountCode != null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('رمز الخصم صحيح! خصم ${discountCode.discountValue}%'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('رمز الخصم غير صحيح'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التحقق من رمز الخصم: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _selectPlan(SubscriptionPlan plan) {
    // حساب السعر النهائي مع الخصم
    _calculateFinalPrice(plan).then((calculation) {
      if (mounted && calculation != null) {
        _showPlanConfirmation(plan, calculation);
      }
    });
  }

  Future<DiscountCalculation?> _calculateFinalPrice(SubscriptionPlan plan) async {
    try {
      final calculation = await ref.read(
        subscriptionPriceCalculationProvider(
          planId: int.parse(plan.id),
          billingCycle: selectedCycle.name,
          discountCode: selectedDiscountCode,
        ).future,
      );
      return calculation;
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حساب السعر: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return null;
    }
  }

  void _showPlanConfirmation(SubscriptionPlan plan, DiscountCalculation calculation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تأكيد الاشتراك'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('خطة: ${plan.nameAr}'),
            Text('دورة الفوترة: ${selectedCycle == BillingCycle.monthly ? "شهرية" : "سنوية"}'),
            const SizedBox(height: 8),
            Text('السعر الأصلي: ${calculation.originalAmount.toStringAsFixed(0)} د.ل'),
            if (calculation.discountAmount > 0) ...[
              Text('الخصم: ${calculation.discountAmount.toStringAsFixed(0)} د.ل'),
              Text('السعر النهائي: ${calculation.finalAmount.toStringAsFixed(0)} د.ل'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _createSubscription(plan);
            },
            child: const Text('تأكيد الاشتراك'),
          ),
        ],
      ),
    );
  }

  void _createSubscription(SubscriptionPlan plan) async {
    try {
      await ref.read(createSubscriptionNotifierProvider.notifier).createSubscription(
        planId: int.parse(plan.id),
        billingCycle: selectedCycle.name,
        discountCode: selectedDiscountCode,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء الاشتراك بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(); // العودة للشاشة السابقة
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء الاشتراك: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showPlanComparison(List<SubscriptionPlanWithDiscounts> plans) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'مقارنة الخطط',
                  style: CarNowTextStyles.categoryTitle.copyWith(fontWeight: FontWeight.bold),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: plans.map((planWithDiscount) {
                    final plan = planWithDiscount.plan;
                    return ListTile(
                      title: Text(plan.nameAr),
                      subtitle: Text(plan.descriptionAr),
                      trailing: Text(
                        '${plan.monthlyPriceLD.toStringAsFixed(0)} د.ل/شهر',
                        style: CarNowTextStyles.fieldLabel.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      onTap: () {
                        Navigator.of(context).pop();
                        _selectPlan(plan);
                      },
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}