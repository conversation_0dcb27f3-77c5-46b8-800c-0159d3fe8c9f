import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';


/// شاشة شروط وأحكام البائعين
class SellerTermsScreen extends HookConsumerWidget {
  const SellerTermsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAccepted = useState<bool>(false);

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('شروط وأحكام البائعين'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildWelcomeSection(context),
                  const SizedBox(height: 24),
                  _buildTermsContent(context),
                ],
              ),
            ),
          ),
          _buildBottomSection(context, isAccepted),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.store, color: Theme.of(context).colorScheme.primary, size: 32),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  'مرحباً بك في عائلة البائعين',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Text(
            'قبل البدء، يرجى قراءة والموافقة على الشروط والأحكام التالية. هذه الشروط تضمن تجربة آمنة ومربحة للجميع.',
            style: TextStyle(fontSize: 16, height: 1.5, color: Colors.black87),
          ),
        ],
      ),
    );
  }

  Widget _buildTermsContent(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTermSection(context, '1. شروط العضوية', [
            'يجب أن تكون أكبر من 18 عاماً أو تمثل شركة مسجلة قانونياً',
            'جميع المعلومات المقدمة يجب أن تكون صحيحة ودقيقة',
            'يجب توفير رقم هاتف صالح (يفضل واتساب) للتواصل السريع',
            'العنوان المقدم يجب أن يكون عنوانك الفعلي للعمل',
          ]),
          _buildTermSection(context, '2. المنتجات والإعلانات', [
            'جميع المنتجات يجب أن تكون قانونية وأصلية',
            'ممنوع بيع المنتجات المقلدة أو المسروقة',
            'يجب تقديم وصف دقيق وصور واضحة للمنتجات',
            'الأسعار يجب أن تكون واضحة وعادلة',
            'ممنوع الإعلانات المضللة أو الكاذبة',
          ]),
          _buildTermSection(context, '3. المعاملات المالية', [
            'رسوم الاشتراك الشهري حسب الخطة المختارة',
            'الدفع يتم مقدماً لكل دورة فوترة',
            'عدم الدفع يؤدي إلى تعليق الحساب',
            'لا يمكن استرداد الرسوم إلا في حالات خاصة',
          ]),
          _buildTermSection(context, '4. التواصل والدعم', [
            'يجب الرد على استفسارات العملاء خلال 24 ساعة',
            'التعامل بأدب واحترام مع جميع العملاء',
            'الالتزام بمواعيد التسليم المحددة',
            'توفير خدمة عملاء ممتازة',
          ]),
          _buildTermSection(context, '5. السياسات والقوانين', [
            'الالتزام بقوانين الدولة المحلية',
            'احترام حقوق الملكية الفكرية',
            'عدم التلاعب في النظام أو محاولة خداعه',
            'الإبلاغ عن أي مشاكل تقنية فوراً',
          ]),
          _buildTermSection(context, '6. إنهاء العضوية', [
            'يمكن إنهاء العضوية في أي وقت مع إشعار مسبق',
            'المنصة تحتفظ بحق إنهاء العضوية في حالة مخالفة الشروط',
            'البيانات تبقى محفوظة لمدة 30 يوم بعد الإنهاء',
          ]),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.amber.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.amber.shade700),
                    const SizedBox(width: 8),
                    Text(
                      'ملاحظة مهمة',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.amber.shade700,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const Text(
                  'هذه الشروط قابلة للتحديث. سيتم إشعارك بأي تغييرات مهمة عبر البريد الإلكتروني أو الإشعارات داخل التطبيق.',
                  style: TextStyle(height: 1.5),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTermSection(BuildContext context, String title, List<String> points) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          ...points.map(
            (point) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 6),
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      point,
                      style: const TextStyle(
                        fontSize: 15,
                        height: 1.5,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomSection(
    BuildContext context,
    ValueNotifier<bool> isAccepted,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: CheckboxListTile(
              title: const Text(
                'أوافق على جميع الشروط والأحكام المذكورة أعلاه',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
              subtitle: const Text(
                'لن تتمكن من المتابعة بدون الموافقة على هذه الشروط',
                style: TextStyle(fontSize: 14),
              ),
              value: isAccepted.value,
              onChanged: (value) => isAccepted.value = value ?? false,
              controlAffinity: ListTileControlAffinity.leading,
              activeColor: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.grey.shade700,
                    side: BorderSide(color: Colors.grey.shade300),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    minimumSize: const Size(0, 50),
                  ),
                  child: const Text('إلغاء', style: TextStyle(fontSize: 16)),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                flex: 2,
                child: ElevatedButton(
                  onPressed: isAccepted.value
                      ? () {
                          Navigator.of(
                            context,
                          ).pop(true); // إرجاع true للموافقة
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    minimumSize: const Size(0, 50),
                  ),
                  child: const Text(
                    'موافق، المتابعة للتسجيل',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
