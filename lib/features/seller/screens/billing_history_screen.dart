import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../../core/widgets/app_error_widget.dart';
import '../models/subscription_model.dart';
import '../models/seller_enums.dart';
import '../providers/subscription_provider.dart';
import '../providers/subscription_analytics_provider.dart';

class BillingHistoryScreen extends HookConsumerWidget {
  const BillingHistoryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final billingHistoryAsync = ref.watch(subscriptionBillingHistoryProvider);
    final analytics = ref.watch(subscriptionAnalyticsProvider);

    // Track billing history view
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        billingHistoryAsync.whenData((billings) {
          final totalAmount = billings.fold<double>(
            0,
            (sum, b) => sum + b.amount,
          );
          analytics.trackBillingHistoryViewEvent(
            invoiceCount: billings.length,
            totalAmount: totalAmount,
          );
        });
      });
      return null;
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'سجل الفواتير',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        foregroundColor: Theme.of(context).textTheme.bodyLarge?.color,
        actions: [
          IconButton(
            onPressed: () {
              ref.invalidate(subscriptionBillingHistoryProvider);
            },
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: billingHistoryAsync.when(
        data: (billings) => billings.isEmpty
            ? _buildEmptyState(context)
            : _buildBillingList(context, billings),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => AppErrorWidget(
          message: error.toString(),
          onRetry: () => ref.refresh(subscriptionBillingHistoryProvider),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 80,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 24),
            Text(
              'لا توجد فواتير بعد',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'ستظهر فواتير الاشتراك والدفعات هنا',
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBillingList(
    BuildContext context,
    List<SubscriptionBilling> billings,
  ) {
    return Column(
      children: [
        // Summary card
        _buildBillingSummary(context, billings),

        // Billing list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: billings.length,
            itemBuilder: (context, index) {
              return _buildBillingCard(context, billings[index]);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildBillingSummary(
    BuildContext context,
    List<SubscriptionBilling> billings,
  ) {
    final paidBillings = billings
        .where((b) => b.status == ListingPaymentStatus.paid)
        .toList();
    final totalPaid = paidBillings.fold<double>(
      0,
      (sum, billing) => sum + billing.amount,
    );
    final pendingBillings = billings
        .where((b) => b.status == ListingPaymentStatus.pending)
        .toList();
    final totalPending = pendingBillings.fold<double>(
      0,
      (sum, billing) => sum + billing.amount,
    );

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.primary.withValues(alpha: 204)],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 77),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ملخص الفواتير',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  'إجمالي المدفوع',
                  '${totalPaid.toStringAsFixed(1)} د.ل',
                  '${paidBillings.length} فاتورة',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryItem(
                  'في الانتظار',
                  '${totalPending.toStringAsFixed(1)} د.ل',
                  '${pendingBillings.length} فاتورة',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String title, String amount, String count) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 230),
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          amount,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          count,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 204),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildBillingCard(BuildContext context, SubscriptionBilling billing) {
    final dateFormat = DateFormat('dd/MM/yyyy');
    final status = billing.status;
    final statusColor = _getStatusColor(context, status);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 26),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ExpansionTile(
        tilePadding: const EdgeInsets.all(20),
        childrenPadding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: statusColor.withValues(alpha: 26),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(_getStatusIcon(status), color: statusColor, size: 24),
        ),
        title: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'فاتورة ${_getCycleText(billing.cycle)}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    dateFormat.format(billing.billingDate),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${billing.amount.toStringAsFixed(1)} د.ل',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: statusColor,
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 26),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _getStatusText(status),
                    style: TextStyle(
                      color: statusColor,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        children: [_buildBillingDetails(context, billing)],
      ),
    );
  }

  Widget _buildBillingDetails(
    BuildContext context,
    SubscriptionBilling billing,
  ) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تفاصيل الفاتورة',
            style: Theme.of(
              context,
            ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),

          if (billing.transactionId != null)
            _buildDetailRow(context, 'رقم المعاملة', billing.transactionId!),

          if (billing.invoiceId != null)
            _buildDetailRow(context, 'رقم الفاتورة', billing.invoiceId!),

          _buildDetailRow(context, 'العملة', billing.currency),
          _buildDetailRow(
            context,
            'المبلغ الأساسي',
            '${billing.amount.toStringAsFixed(1)} د.ل',
          ),

          if (billing.discountAmount > 0)
            _buildDetailRow(
              context,
              'الخصم',
              '-${billing.discountAmount.toStringAsFixed(1)} د.ل',
              Colors.green,
            ),

          if (billing.taxAmount > 0)
            _buildDetailRow(
              context,
              'الضريبة',
              '${billing.taxAmount.toStringAsFixed(1)} د.ل',
            ),

          const Divider(height: 20),

          _buildDetailRow(
            context,
            'الإجمالي',
            '${(billing.amount + billing.taxAmount - billing.discountAmount).toStringAsFixed(1)} د.ل',
            Colors.black,
          ),

          const SizedBox(height: 12),

          if (billing.paidDate != null)
            _buildDetailRow(
              context,
              'تاريخ الدفع',
              dateFormat.format(billing.paidDate!),
            ),

          if (billing.dueDate != null)
            _buildDetailRow(
              context,
              'تاريخ الاستحقاق',
              dateFormat.format(billing.dueDate!),
            ),

          if (billing.failedDate != null)
            _buildDetailRow(
              context,
              'تاريخ الفشل',
              dateFormat.format(billing.failedDate!),
              Colors.red,
            ),

          if (billing.failureReason != null) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 26),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.red, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'سبب الفشل: ${billing.failureReason!}',
                      style: const TextStyle(color: Colors.red, fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],

          if (billing.status == ListingPaymentStatus.pending) ...[
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  // Handle payment retry
                  _handlePaymentRetry(context, billing);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'إعادة محاولة الدفع',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value, [
    Color? valueColor,
  ]) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: valueColor,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(BuildContext context, ListingPaymentStatus status) {
    switch (status) {
      case ListingPaymentStatus.paid:
        return Theme.of(context).colorScheme.primary;
      case ListingPaymentStatus.pending:
        return Colors.orange;
      case ListingPaymentStatus.failed:
        return Colors.red;
      case ListingPaymentStatus.refunded:
        return Colors.blue;
      case ListingPaymentStatus.waived:
        return Colors.grey;
      case ListingPaymentStatus.overdue:
        return Colors.deepOrange;
    }
  }

  IconData _getStatusIcon(ListingPaymentStatus status) {
    switch (status) {
      case ListingPaymentStatus.paid:
        return Icons.check_circle;
      case ListingPaymentStatus.pending:
        return Icons.schedule;
      case ListingPaymentStatus.failed:
        return Icons.error;
      case ListingPaymentStatus.refunded:
        return Icons.undo;
      case ListingPaymentStatus.waived:
        return Icons.money_off;
      case ListingPaymentStatus.overdue:
        return Icons.warning;
    }
  }

  String _getStatusText(ListingPaymentStatus status) {
    switch (status) {
      case ListingPaymentStatus.paid:
        return 'مدفوعة';
      case ListingPaymentStatus.pending:
        return 'في الانتظار';
      case ListingPaymentStatus.failed:
        return 'فشلت';
      case ListingPaymentStatus.refunded:
        return 'مستردة';
      case ListingPaymentStatus.waived:
        return 'معفاة';
      case ListingPaymentStatus.overdue:
        return 'متأخرة';
    }
  }

  String _getCycleText(BillingCycle cycle) {
    switch (cycle) {
      case BillingCycle.monthly:
        return 'شهرية';
      case BillingCycle.yearly:
        return 'سنوية';
    }
  }

  void _handlePaymentRetry(BuildContext context, SubscriptionBilling billing) {
    // Show payment retry dialog or navigate to payment screen
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة محاولة الدفع'),
        content: Text(
          'هل تريد إعادة محاولة دفع فاتورة بقيمة ${billing.amount.toStringAsFixed(1)} د.ل؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Handle payment logic here
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('سيتم تحويلك إلى صفحة الدفع...')),
              );
            },
            child: const Text('دفع'),
          ),
        ],
      ),
    );
  }
}
