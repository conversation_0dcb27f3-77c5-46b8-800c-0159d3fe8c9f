#!/usr/bin/env dart

/// <PERSON>ript to fix common syntax errors in CarNow project
/// Fixes patterns like:
/// - static constructors -> const constructors
/// - malformed function signatures
/// - static EdgeInsets -> const EdgeInsets
/// - malformed conditional expressions

import 'dart:io';

void main() async {
  print('🔧 Starting syntax error fixes...');
  
  final libDir = Directory('lib');
  if (!libDir.existsSync()) {
    print('❌ lib directory not found');
    exit(1);
  }

  final dartFiles = await _findDartFiles(libDir);
  print('📁 Found ${dartFiles.length} Dart files in lib/');

  int fixedFiles = 0;
  int totalFixes = 0;

  for (final file in dartFiles) {
    final fixes = await _fixFileErrors(file);
    if (fixes > 0) {
      fixedFiles++;
      totalFixes += fixes;
      print('✅ Fixed $fixes errors in ${file.path}');
    }
  }

  print('\n🎯 Fix Results:');
  print('   Files processed: ${dartFiles.length}');
  print('   Files fixed: $fixedFiles');
  print('   Total fixes applied: $totalFixes');
  
  if (totalFixes > 0) {
    print('\n✨ Syntax errors fixed successfully!');
  } else {
    print('\n✅ No syntax errors found.');
  }
}

Future<List<File>> _findDartFiles(Directory dir) async {
  final dartFiles = <File>[];
  
  await for (final entity in dir.list(recursive: true)) {
    if (entity is File && entity.path.endsWith('.dart')) {
      dartFiles.add(entity);
    }
  }
  
  return dartFiles;
}

Future<int> _fixFileErrors(File file) async {
  try {
    String content = await file.readAsString();
    final originalContent = content;
    int fixes = 0;

    // Fix 1: static constructors -> const constructors
    final staticConstructorPattern = RegExp(r'(\s+)static\s+(\w+)\s*\(');
    content = content.replaceAllMapped(staticConstructorPattern, (match) {
      fixes++;
      return '${match.group(1)}const ${match.group(2)}(';
    });

    // Fix 2: malformed function signatures with (BuildContext context) => >
    final malformedFunctionPattern = RegExp(r'\)\s*\(BuildContext\s+context\)\s*=>\s*>\s*');
    content = content.replaceAll(malformedFunctionPattern, ') => ');
    if (malformedFunctionPattern.hasMatch(originalContent)) {
      fixes++;
    }

    // Fix 3: static EdgeInsets -> const EdgeInsets
    final staticEdgeInsetsPattern = RegExp(r'static\s+EdgeInsets');
    content = content.replaceAll(staticEdgeInsetsPattern, 'const EdgeInsets');
    if (staticEdgeInsetsPattern.hasMatch(originalContent)) {
      fixes++;
    }

    // Fix 4: static Icon -> const Icon
    final staticIconPattern = RegExp(r'static\s+Icon\s*\(');
    content = content.replaceAll(staticIconPattern, 'const Icon(');
    if (staticIconPattern.hasMatch(originalContent)) {
      fixes++;
    }

    // Fix 5: static SizedBox -> const SizedBox
    final staticSizedBoxPattern = RegExp(r'static\s+SizedBox\s*\(');
    content = content.replaceAll(staticSizedBoxPattern, 'const SizedBox(');
    if (staticSizedBoxPattern.hasMatch(originalContent)) {
      fixes++;
    }

    // Fix 6: static Text -> const Text
    final staticTextPattern = RegExp(r'static\s+Text\s*\(');
    content = content.replaceAll(staticTextPattern, 'const Text(');
    if (staticTextPattern.hasMatch(originalContent)) {
      fixes++;
    }

    // Fix 7: malformed conditional expressions !(BuildContext context) => null
    final malformedConditionalPattern = RegExp(r'\s+!\(BuildContext\s+context\)\s*=>\s*null');
    content = content.replaceAll(malformedConditionalPattern, ' != null');
    if (malformedConditionalPattern.hasMatch(originalContent)) {
      fixes++;
    }

    // Fix 8: Fix _buildOperationTile calls to include context
    final buildOperationTilePattern = RegExp(r'(\s+)_buildOperationTile\(\s*\n\s*icon:');
    content = content.replaceAllMapped(buildOperationTilePattern, (match) {
      fixes++;
      return '${match.group(1)}_buildOperationTile(\n${match.group(1)}  context,\n${match.group(1)}  icon:';
    });

    // Fix 9: Remove unused app_colors.dart imports (simplified)
    if (content.contains('app_colors.dart') && !content.contains('AppColors.')) {
      final lines = content.split('\n');
      final filteredLines = <String>[];
      bool removedImport = false;

      for (final line in lines) {
        if (line.contains('app_colors.dart') && line.trim().startsWith('import')) {
          removedImport = true;
          // Skip this line (remove the import)
        } else {
          filteredLines.add(line);
        }
      }

      if (removedImport) {
        content = filteredLines.join('\n');
        fixes++;
      }
    }

    // Write back if changes were made
    if (content != originalContent) {
      await file.writeAsString(content);
    }

    return fixes;
  } catch (e) {
    print('❌ Error processing ${file.path}: $e');
    return 0;
  }
}
