package handlers

import (
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"carnow-backend/internal/config"
	"carnow-backend/internal/shared/validation"
)

// SessionHandlers handles session management endpoints
type SessionHandlers struct {
	config    *config.Config
	logger    *zap.Logger
	validator *validation.CustomValidator
}

// Session Request/Response structures
type CreateSessionRequest struct {
	DeviceInfo    map[string]interface{} `json:"device_info,omitempty"`
	IPAddress     string                 `json:"ip_address,omitempty"`
	UserAgent     string                 `json:"user_agent,omitempty"`
	RefreshToken  string                 `json:"refresh_token" binding:"required"`
}

type SessionData struct {
	ID           string                 `json:"id"`
	UserID       string                 `json:"user_id"`
	DeviceInfo   map[string]interface{} `json:"device_info,omitempty"`
	IPAddress    string                 `json:"ip_address,omitempty"`
	UserAgent    string                 `json:"user_agent,omitempty"`
	IsActive     bool                   `json:"is_active"`
	LastActivity time.Time              `json:"last_activity"`
	CreatedAt    time.Time              `json:"created_at"`
	ExpiresAt    time.Time              `json:"expires_at"`
}

type SessionActivity struct {
	ID          string    `json:"id"`
	SessionID   string    `json:"session_id"`
	Action      string    `json:"action"`
	IPAddress   string    `json:"ip_address,omitempty"`
	UserAgent   string    `json:"user_agent,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
}

type DeviceFingerprint struct {
	Fingerprint string                 `json:"fingerprint"`
	DeviceInfo  map[string]interface{} `json:"device_info"`
	CreatedAt   time.Time              `json:"created_at"`
}

type SessionResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// NewSessionHandlers creates new session handlers
func NewSessionHandlers(config *config.Config, logger *zap.Logger) *SessionHandlers {
	return &SessionHandlers{
		config:    config,
		logger:    logger,
		validator: validation.NewCustomValidator(),
	}
}

// CreateSession creates a new session for the user
// POST /api/v1/sessions
func (h *SessionHandlers) CreateSession(c *gin.Context) {
	var req CreateSessionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid create session request",
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, SessionResponse{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	// Validate request
	if err := h.validator.Validate(req); err != nil {
		c.JSON(http.StatusBadRequest, SessionResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, SessionResponse{
			Success: false,
			Error:   "User not authenticated",
		})
		return
	}

	userIDStr := userID.(string)

	// Generate session ID
	sessionID := h.generateSessionID()

	// Create session data
	session := SessionData{
		ID:           sessionID,
		UserID:       userIDStr,
		DeviceInfo:   req.DeviceInfo,
		IPAddress:    req.IPAddress,
		UserAgent:    req.UserAgent,
		IsActive:     true,
		LastActivity: time.Now(),
		CreatedAt:    time.Now(),
		ExpiresAt:    time.Now().Add(24 * time.Hour), // 24 hour session
	}

	// TODO: Store session in database (implement when database is available)
	// For now, return success response
	h.logger.Info("Session created",
		zap.String("session_id", sessionID),
		zap.String("user_id", userIDStr),
		zap.String("ip_address", req.IPAddress),
	)

	c.JSON(http.StatusCreated, SessionResponse{
		Success: true,
		Message: "Session created successfully",
		Data:    session,
	})
}

// GetUserSessions retrieves all active sessions for the current user
// GET /api/v1/sessions
func (h *SessionHandlers) GetUserSessions(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, SessionResponse{
			Success: false,
			Error:   "User not authenticated",
		})
		return
	}

	userIDStr := userID.(string)

	// TODO: Retrieve sessions from database (implement when database is available)
	// For now, return empty list
	sessions := []SessionData{}

	h.logger.Info("Retrieved user sessions",
		zap.String("user_id", userIDStr),
		zap.Int("count", len(sessions)),
	)

	c.JSON(http.StatusOK, SessionResponse{
		Success: true,
		Message: "Sessions retrieved successfully",
		Data:    sessions,
	})
}

// RevokeSession revokes a specific session
// DELETE /api/v1/sessions/:session_id
func (h *SessionHandlers) RevokeSession(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		c.JSON(http.StatusBadRequest, SessionResponse{
			Success: false,
			Error:   "Session ID is required",
		})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, SessionResponse{
			Success: false,
			Error:   "User not authenticated",
		})
		return
	}

	userIDStr := userID.(string)

	// TODO: Revoke session in database (implement when database is available)
	// For now, return success response
	h.logger.Info("Session revoked",
		zap.String("session_id", sessionID),
		zap.String("user_id", userIDStr),
	)

	c.JSON(http.StatusOK, SessionResponse{
		Success: true,
		Message: "Session revoked successfully",
	})
}

// RevokeAllSessions revokes all sessions for the current user
// DELETE /api/v1/sessions
func (h *SessionHandlers) RevokeAllSessions(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, SessionResponse{
			Success: false,
			Error:   "User not authenticated",
		})
		return
	}

	userIDStr := userID.(string)

	// TODO: Revoke all sessions in database (implement when database is available)
	// For now, return success response
	h.logger.Info("All sessions revoked",
		zap.String("user_id", userIDStr),
	)

	c.JSON(http.StatusOK, SessionResponse{
		Success: true,
		Message: "All sessions revoked successfully",
	})
}

// RefreshSession refreshes a session using refresh token
// POST /api/v1/sessions/refresh
func (h *SessionHandlers) RefreshSession(c *gin.Context) {
	type RefreshRequest struct {
		RefreshToken string `json:"refresh_token" binding:"required"`
	}

	var req RefreshRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid refresh session request",
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, SessionResponse{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	// TODO: Validate refresh token and refresh session (implement when database is available)
	// For now, return success response
	h.logger.Info("Session refreshed",
		zap.String("client_ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, SessionResponse{
		Success: true,
		Message: "Session refreshed successfully",
	})
}

// ValidateSession validates a session token
// POST /api/v1/sessions/validate
func (h *SessionHandlers) ValidateSession(c *gin.Context) {
	type ValidateRequest struct {
		SessionToken string `json:"session_token" binding:"required"`
	}

	var req ValidateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid validate session request",
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, SessionResponse{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	// TODO: Validate session token (implement when database is available)
	// For now, return success response
	h.logger.Info("Session validated",
		zap.String("client_ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, SessionResponse{
		Success: true,
		Message: "Session is valid",
	})
}

// GetSessionActivity retrieves session activity for the current user
// GET /api/v1/sessions/activity
func (h *SessionHandlers) GetSessionActivity(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, SessionResponse{
			Success: false,
			Error:   "User not authenticated",
		})
		return
	}

	userIDStr := userID.(string)

	// TODO: Retrieve session activity from database (implement when database is available)
	// For now, return empty list
	activities := []SessionActivity{}

	h.logger.Info("Retrieved session activity",
		zap.String("user_id", userIDStr),
		zap.Int("count", len(activities)),
	)

	c.JSON(http.StatusOK, SessionResponse{
		Success: true,
		Message: "Session activity retrieved successfully",
		Data:    activities,
	})
}

// GenerateDeviceFingerprint generates a device fingerprint
// POST /api/v1/sessions/device-fingerprint
func (h *SessionHandlers) GenerateDeviceFingerprint(c *gin.Context) {
	type FingerprintRequest struct {
		DeviceInfo map[string]interface{} `json:"device_info" binding:"required"`
	}

	var req FingerprintRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid device fingerprint request",
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, SessionResponse{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	// Generate device fingerprint
	fingerprint := h.generateDeviceFingerprint(req.DeviceInfo)

	deviceFingerprint := DeviceFingerprint{
		Fingerprint: fingerprint,
		DeviceInfo:  req.DeviceInfo,
		CreatedAt:   time.Now(),
	}

	h.logger.Info("Device fingerprint generated",
		zap.String("fingerprint", fingerprint),
		zap.String("client_ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, SessionResponse{
		Success: true,
		Message: "Device fingerprint generated successfully",
		Data:    deviceFingerprint,
	})
}

// Helper methods

// generateSessionID generates a unique session ID
func (h *SessionHandlers) generateSessionID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// generateDeviceFingerprint generates a device fingerprint from device info
func (h *SessionHandlers) generateDeviceFingerprint(deviceInfo map[string]interface{}) string {
	// Create a deterministic string from device info
	deviceInfoBytes, _ := json.Marshal(deviceInfo)
	
	// Generate hash from device info
	hash := fmt.Sprintf("%x", deviceInfoBytes)
	
	// Take first 16 characters as fingerprint
	if len(hash) > 16 {
		return hash[:16]
	}
	return hash
} 