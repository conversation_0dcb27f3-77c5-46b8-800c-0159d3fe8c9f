import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/core/error/subscription_error_handler.dart';
import 'package:carnow/core/models/subscription_error.dart';

void main() {
  group('SubscriptionErrorHandler', () {
    late Widget testApp;

    setUp(() {
      testApp = MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (context) => ElevatedButton(
              onPressed: () {
                // Test button for triggering error dialogs
              },
              child: const Text('Test Button'),
            ),
          ),
        ),
      );
    });

    group('Network Error Handling', () {
      testWidgets('shows network error dialog with retry option', (
        tester,
      ) async {
        await tester.pumpWidget(testApp);

        final context = tester.element(find.byType(ElevatedButton));
        final error = SubscriptionError.networkError(
          message: 'Network connection failed',
          code: 'NETWORK_ERROR',
        );

        bool retryPressed = false;

        // Show the error dialog
        SubscriptionErrorHandler.handleSubscriptionError(
          context: context,
          error: error,
          onRetry: () => retryPressed = true,
        );

        await tester.pumpAndSettle();

        // Verify dialog is shown
        expect(find.byType(AlertDialog), findsOneWidget);
        expect(find.text('خطأ في الاتصال'), findsOneWidget);
        expect(
          find.text('خطأ في الاتصال: تحقق من اتصال الإنترنت وحاول مرة أخرى'),
          findsOneWidget,
        );
        expect(
          find.text('تحقق من اتصال الإنترنت وحاول مرة أخرى'),
          findsOneWidget,
        );

        // Verify buttons are present
        expect(find.text('إلغاء'), findsOneWidget);
        expect(find.text('إعادة المحاولة'), findsOneWidget);
        expect(find.byIcon(Icons.wifi_off_rounded), findsOneWidget);
        expect(find.byIcon(Icons.refresh_rounded), findsOneWidget);

        // Test retry button
        await tester.tap(find.text('إعادة المحاولة'));
        await tester.pumpAndSettle();

        expect(retryPressed, isTrue);
        expect(find.byType(AlertDialog), findsNothing);
      });

      testWidgets('shows network error dialog without retry option', (
        tester,
      ) async {
        await tester.pumpWidget(testApp);

        final context = tester.element(find.byType(ElevatedButton));
        final error = SubscriptionError.networkError(
          message: 'Network connection failed',
          code: 'NETWORK_ERROR',
        );

        // Show the error dialog without retry option
        SubscriptionErrorHandler.handleSubscriptionError(
          context: context,
          error: error,
          showRetryOption: (context) => false,
        );

        await tester.pumpAndSettle();

        // Verify dialog is shown
        expect(find.byType(AlertDialog), findsOneWidget);
        expect(find.text('خطأ في الاتصال'), findsOneWidget);

        // Verify only cancel button is present
        expect(find.text('إلغاء'), findsOneWidget);
        expect(find.text('إعادة المحاولة'), findsNothing);
      });
    });

    group('Database Error Handling', () {
      testWidgets('shows database error dialog with retry option', (
        tester,
      ) async {
        await tester.pumpWidget(testApp);

        final context = tester.element(find.byType(ElevatedButton));
        final error = SubscriptionError.databaseError(
          message: 'Database connection failed',
          code: 'DB_ERROR',
        );

        bool retryPressed = false;

        // Show the error dialog
        SubscriptionErrorHandler.handleSubscriptionError(
          context: context,
          error: error,
          onRetry: () => retryPressed = true,
        );

        await tester.pumpAndSettle();

        // Verify dialog is shown
        expect(find.byType(AlertDialog), findsOneWidget);
        expect(find.text('خطأ في قاعدة البيانات'), findsOneWidget);
        expect(
          find.text('خطأ في قاعدة البيانات: حاول مرة أخرى لاحقاً'),
          findsOneWidget,
        );
        expect(find.byIcon(Icons.storage_rounded), findsOneWidget);

        // Test retry button
        await tester.tap(find.text('إعادة المحاولة'));
        await tester.pumpAndSettle();

        expect(retryPressed, isTrue);
      });
    });

    group('Validation Error Handling', () {
      testWidgets('shows validation error dialog with field errors', (
        tester,
      ) async {
        await tester.pumpWidget(testApp);

        final context = tester.element(find.byType(ElevatedButton));
        final error = SubscriptionError.validationError(
          message: 'Validation failed',
          fieldErrors: {
            'storeName': 'اسم المتجر مطلوب',
            'phone': 'رقم الهاتف غير صحيح',
            'city': 'المدينة مطلوبة',
          },
          code: 'VALIDATION_ERROR',
        );

        // Show the error dialog
        SubscriptionErrorHandler.handleSubscriptionError(
          context: context,
          error: error,
        );

        await tester.pumpAndSettle();

        // Verify dialog is shown
        expect(find.byType(AlertDialog), findsOneWidget);
        expect(find.text('بيانات غير صحيحة'), findsOneWidget);
        expect(
          find.text('بيانات غير صحيحة: يرجى مراجعة البيانات المدخلة'),
          findsOneWidget,
        );
        expect(find.byIcon(Icons.error_outline_rounded), findsOneWidget);

        // Verify field errors are shown
        expect(find.text('الأخطاء:'), findsOneWidget);
        expect(
          find.textContaining('اسم المتجر: اسم المتجر مطلوب'),
          findsOneWidget,
        );
        expect(
          find.textContaining('رقم الهاتف: رقم الهاتف غير صحيح'),
          findsOneWidget,
        );
        expect(find.textContaining('المدينة: المدينة مطلوبة'), findsOneWidget);

        // Verify only OK button is present (no retry for validation errors)
        expect(find.text('موافق'), findsOneWidget);
        expect(find.text('إعادة المحاولة'), findsNothing);
      });
    });

    group('Navigation Error Handling', () {
      testWidgets('shows navigation error dialog', (tester) async {
        await tester.pumpWidget(testApp);

        final context = tester.element(find.byType(ElevatedButton));
        final error = SubscriptionError.navigationError(
          message: 'Navigation failed',
          attemptedRoute: '/subscription/status',
          code: 'NAV_ERROR',
        );

        bool retryPressed = false;

        // Show the error dialog
        SubscriptionErrorHandler.handleSubscriptionError(
          context: context,
          error: error,
          onRetry: () => retryPressed = true,
        );

        await tester.pumpAndSettle();

        // Verify dialog is shown
        expect(find.byType(AlertDialog), findsOneWidget);
        expect(find.text('خطأ في التنقل'), findsOneWidget);
        expect(find.text('خطأ في التنقل: حاول مرة أخرى'), findsOneWidget);
        expect(find.byIcon(Icons.navigation_rounded), findsOneWidget);

        // Test retry button
        await tester.tap(find.text('إعادة المحاولة'));
        await tester.pumpAndSettle();

        expect(retryPressed, isTrue);
      });
    });

    group('Authentication Error Handling', () {
      testWidgets('shows authentication error dialog', (tester) async {
        await tester.pumpWidget(testApp);

        final context = tester.element(find.byType(ElevatedButton));
        final error = SubscriptionError.authenticationError(
          message: 'Authentication failed',
          code: 'AUTH_ERROR',
        );

        // Show the error dialog
        SubscriptionErrorHandler.handleSubscriptionError(
          context: context,
          error: error,
        );

        await tester.pumpAndSettle();

        // Verify dialog is shown
        expect(find.byType(AlertDialog), findsOneWidget);
        expect(find.text('خطأ في المصادقة'), findsOneWidget);
        expect(
          find.text('خطأ في المصادقة: يرجى تسجيل الدخول مرة أخرى'),
          findsOneWidget,
        );
        expect(find.byIcon(Icons.lock_outline_rounded), findsOneWidget);

        // Verify only OK button is present (no retry for auth errors)
        expect(find.text('موافق'), findsOneWidget);
        expect(find.text('إعادة المحاولة'), findsNothing);
      });
    });

    group('Server Error Handling', () {
      testWidgets('shows server error dialog with retry for retryable errors', (
        tester,
      ) async {
        await tester.pumpWidget(testApp);

        final context = tester.element(find.byType(ElevatedButton));
        final error = SubscriptionError.serverError(
          message: 'Internal server error',
          code: 'SERVER_ERROR',
          statusCode: 500,
        );

        bool retryPressed = false;

        // Show the error dialog
        SubscriptionErrorHandler.handleSubscriptionError(
          context: context,
          error: error,
          onRetry: () => retryPressed = true,
        );

        await tester.pumpAndSettle();

        // Verify dialog is shown
        expect(find.byType(AlertDialog), findsOneWidget);
        expect(find.text('خطأ في الخادم'), findsOneWidget);
        expect(
          find.text('خطأ في الخادم: حاول مرة أخرى لاحقاً'),
          findsOneWidget,
        );
        expect(find.byIcon(Icons.cloud_off_rounded), findsOneWidget);

        // Verify retry button is present for 5xx errors
        expect(find.text('إعادة المحاولة'), findsOneWidget);

        // Test retry button
        await tester.tap(find.text('إعادة المحاولة'));
        await tester.pumpAndSettle();

        expect(retryPressed, isTrue);
      });

      testWidgets(
        'shows server error dialog without retry for non-retryable errors',
        (tester) async {
          await tester.pumpWidget(testApp);

          final context = tester.element(find.byType(ElevatedButton));
          final error = SubscriptionError.serverError(
            message: 'Bad request',
            code: 'BAD_REQUEST',
            statusCode: 400,
          );

          // Show the error dialog
          SubscriptionErrorHandler.handleSubscriptionError(
            context: context,
            error: error,
          );

          await tester.pumpAndSettle();

          // Verify dialog is shown
          expect(find.byType(AlertDialog), findsOneWidget);
          expect(find.text('خطأ في الخادم'), findsOneWidget);

          // Verify no retry button for 4xx errors
          expect(find.text('إعادة المحاولة'), findsNothing);
        },
      );
    });

    group('Payment Error Handling', () {
      testWidgets('shows payment error dialog', (tester) async {
        await tester.pumpWidget(testApp);

        final context = tester.element(find.byType(ElevatedButton));
        final error = SubscriptionError.paymentError(
          message: 'Payment failed',
          code: 'PAYMENT_ERROR',
          paymentMethod: 'credit_card',
        );

        // Show the error dialog
        SubscriptionErrorHandler.handleSubscriptionError(
          context: context,
          error: error,
        );

        await tester.pumpAndSettle();

        // Verify dialog is shown
        expect(find.byType(AlertDialog), findsOneWidget);
        expect(find.text('خطأ في عملية الدفع'), findsOneWidget);
        expect(
          find.text('خطأ في عملية الدفع: تحقق من طريقة الدفع'),
          findsOneWidget,
        );
        expect(find.byIcon(Icons.payment_rounded), findsOneWidget);

        // Verify only OK button is present (no retry for payment errors)
        expect(find.text('موافق'), findsOneWidget);
        expect(find.text('إعادة المحاولة'), findsNothing);
      });
    });

    group('Business Logic Error Handling', () {
      testWidgets('shows business logic error dialog', (tester) async {
        await tester.pumpWidget(testApp);

        final context = tester.element(find.byType(ElevatedButton));
        final error = SubscriptionError.businessLogicError(
          message: 'Business rule violation',
          code: 'BUSINESS_ERROR',
          businessRule: 'subscription_limit_exceeded',
        );

        // Show the error dialog
        SubscriptionErrorHandler.handleSubscriptionError(
          context: context,
          error: error,
        );

        await tester.pumpAndSettle();

        // Verify dialog is shown
        expect(find.byType(AlertDialog), findsOneWidget);
        expect(find.text('خطأ في العملية'), findsOneWidget);
        expect(
          find.text('خطأ في العملية: Business rule violation'),
          findsOneWidget,
        );
        expect(find.byIcon(Icons.business_rounded), findsOneWidget);

        // Verify only OK button is present (no retry for business logic errors)
        expect(find.text('موافق'), findsOneWidget);
        expect(find.text('إعادة المحاولة'), findsNothing);
      });
    });

    group('Unknown Error Handling', () {
      testWidgets('shows unknown error dialog with retry option', (
        tester,
      ) async {
        await tester.pumpWidget(testApp);

        final context = tester.element(find.byType(ElevatedButton));
        final error = SubscriptionError.unknownError(
          message: 'Unknown error occurred',
          code: 'UNKNOWN_ERROR',
        );

        bool retryPressed = false;

        // Show the error dialog
        SubscriptionErrorHandler.handleSubscriptionError(
          context: context,
          error: error,
          onRetry: () => retryPressed = true,
        );

        await tester.pumpAndSettle();

        // Verify dialog is shown
        expect(find.byType(AlertDialog), findsOneWidget);
        expect(find.text('خطأ غير متوقع'), findsOneWidget);
        expect(find.text('حدث خطأ غير متوقع: حاول مرة أخرى'), findsOneWidget);
        expect(find.byIcon(Icons.help_outline_rounded), findsOneWidget);

        // Test retry button
        await tester.tap(find.text('إعادة المحاولة'));
        await tester.pumpAndSettle();

        expect(retryPressed, isTrue);
      });
    });

    group('Error SnackBar', () {
      testWidgets('shows error snackbar with retry action', (tester) async {
        await tester.pumpWidget(testApp);

        final context = tester.element(find.byType(ElevatedButton));
        final error = SubscriptionError.networkError(
          message: 'Network error',
          code: 'NETWORK_ERROR',
        );

        bool retryPressed = false;

        // Show the error snackbar
        SubscriptionErrorHandler.showErrorSnackBar(
          context: context,
          error: error,
          onRetry: () => retryPressed = true,
        );

        await tester.pumpAndSettle();

        // Verify snackbar is shown
        expect(find.byType(SnackBar), findsOneWidget);
        expect(
          find.text('خطأ في الاتصال: تحقق من اتصال الإنترنت وحاول مرة أخرى'),
          findsOneWidget,
        );
        expect(find.text('إعادة المحاولة'), findsOneWidget);

        // Test retry action
        await tester.tap(find.text('إعادة المحاولة'));
        await tester.pumpAndSettle();

        expect(retryPressed, isTrue);
      });

      testWidgets(
        'shows error snackbar without retry for non-retryable errors',
        (tester) async {
          await tester.pumpWidget(testApp);

          final context = tester.element(find.byType(ElevatedButton));
          final error = SubscriptionError.validationError(
            message: 'Validation error',
            fieldErrors: {'field': 'error'},
            code: 'VALIDATION_ERROR',
          );

          // Show the error snackbar
          SubscriptionErrorHandler.showErrorSnackBar(
            context: context,
            error: error,
          );

          await tester.pumpAndSettle();

          // Verify snackbar is shown without retry action
          expect(find.byType(SnackBar), findsOneWidget);
          expect(
            find.text('بيانات غير صحيحة: يرجى مراجعة البيانات المدخلة'),
            findsOneWidget,
          );
          expect(find.text('إعادة المحاولة'), findsNothing);
        },
      );
    });

    group('Error Card Widget', () {
      testWidgets('builds error card with retry option', (tester) async {
        final error = SubscriptionError.networkError(
          message: 'Network error',
          code: 'NETWORK_ERROR',
        );

        bool retryPressed = false;
        bool dismissPressed = false;

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) => SubscriptionErrorHandler.buildErrorCard(
                  context: context,
                  error: error,
                  onRetry: () => retryPressed = true,
                  onDismiss: () => dismissPressed = true,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify error card is shown
        expect(find.byType(Card), findsOneWidget);
        expect(
          find.text('خطأ في الاتصال: تحقق من اتصال الإنترنت وحاول مرة أخرى'),
          findsOneWidget,
        );
        expect(find.byIcon(Icons.wifi_off_rounded), findsOneWidget);
        expect(find.byIcon(Icons.close_rounded), findsOneWidget);
        expect(find.text('إعادة المحاولة'), findsOneWidget);

        // Test retry button
        await tester.tap(find.text('إعادة المحاولة'));
        await tester.pumpAndSettle();
        expect(retryPressed, isTrue);

        // Test dismiss button
        await tester.tap(find.byIcon(Icons.close_rounded));
        await tester.pumpAndSettle();
        expect(dismissPressed, isTrue);
      });

      testWidgets('builds error card without retry for non-retryable errors', (
        tester,
      ) async {
        final error = SubscriptionError.validationError(
          message: 'Validation error',
          fieldErrors: {'field': 'error'},
          code: 'VALIDATION_ERROR',
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) => SubscriptionErrorHandler.buildErrorCard(
                  context: context,
                  error: error,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify error card is shown without retry
        expect(find.byType(Card), findsOneWidget);
        expect(
          find.text('بيانات غير صحيحة: يرجى مراجعة البيانات المدخلة'),
          findsOneWidget,
        );
        expect(find.byIcon(Icons.error_outline_rounded), findsOneWidget);
        expect(find.text('إعادة المحاولة'), findsNothing);
      });
    });

    group('Field Name Translation', () {
      test('translates common field names to Arabic', () {
        // This tests the private method indirectly through validation error dialog
        final fieldTranslations = {
          'storeName': 'اسم المتجر',
          'phone': 'رقم الهاتف',
          'city': 'المدينة',
          'address': 'العنوان',
          'description': 'الوصف',
          'planType': 'نوع الخطة',
          'price': 'السعر',
          'userId': 'معرف المستخدم',
          'email': 'البريد الإلكتروني',
          'name': 'الاسم',
          'general': 'عام',
        };

        // Verify that the translations are correctly defined
        expect(fieldTranslations.length, greaterThan(0));
        expect(fieldTranslations['storeName'], equals('اسم المتجر'));
        expect(fieldTranslations['phone'], equals('رقم الهاتف'));
        expect(fieldTranslations['city'], equals('المدينة'));
      });
    });
  });
}
